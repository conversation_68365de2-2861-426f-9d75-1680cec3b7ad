<?php
/**
 * Bulk Delete Members AJAX Handler
 * 
 * This script handles bulk deletion of members with comprehensive safety measures:
 * - Transaction-based deletion for data integrity
 * - Foreign key constraint handling (email_logs, email_tracking)
 * - Profile image cleanup
 * - Detailed logging and error reporting
 * - Confirmation token validation for security
 */

session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Include configuration
require_once '../../config.php';

// Set JSON response header
header('Content-Type: application/json');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Create logs directory if it doesn't exist
$logs_dir = '../../logs';
if (!is_dir($logs_dir)) {
    mkdir($logs_dir, 0755, true);
}

// Log errors to a file for debugging
ini_set('log_errors', 1);
ini_set('error_log', $logs_dir . '/bulk_delete_errors.log');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Get JSON input
$input = file_get_contents('php://input');
$data = json_decode($input, true);

// Validate input data
if (!$data || !isset($data['member_ids']) || !is_array($data['member_ids'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid input data']);
    exit();
}

// Validate confirmation token for security
if (!isset($data['confirmation_token']) || $data['confirmation_token'] !== 'BULK_DELETE_CONFIRMED') {
    echo json_encode(['success' => false, 'message' => 'Confirmation token required']);
    exit();
}

// Sanitize and validate member IDs
$member_ids = array_filter(array_map('intval', $data['member_ids']));

if (empty($member_ids)) {
    echo json_encode(['success' => false, 'message' => 'No valid member IDs provided']);
    exit();
}

// Limit bulk operations to prevent system overload
if (count($member_ids) > 500) {
    echo json_encode(['success' => false, 'message' => 'Bulk delete limited to 500 members at once']);
    exit();
}

try {
    $pdo->beginTransaction();
    
    // First, get member details for logging and file cleanup
    $placeholders = str_repeat('?,', count($member_ids) - 1) . '?';
    $stmt = $pdo->prepare("SELECT id, full_name, email, image_path FROM members WHERE id IN ($placeholders)");
    $stmt->execute($member_ids);
    $members_to_delete = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($members_to_delete)) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => 'No members found with provided IDs']);
        exit();
    }
    
    $deletion_results = [
        'total_requested' => count($member_ids),
        'members_found' => count($members_to_delete),
        'successfully_deleted' => 0,
        'failed_deletions' => [],
        'deleted_members' => [],
        'files_cleaned' => 0
    ];
    
    // Delete related records and members
    foreach ($members_to_delete as $member) {
        $member_id = $member['id'];
        
        try {
            // Helper function to check if table exists
            $checkTable = function($tableName) use ($pdo) {
                try {
                    $stmt = $pdo->query("SHOW TABLES LIKE '$tableName'");
                    return $stmt->rowCount() > 0;
                } catch (PDOException $e) {
                    return false;
                }
            };

            // Helper function to check if column exists in table
            $checkColumn = function($tableName, $columnName) use ($pdo) {
                try {
                    $stmt = $pdo->query("SHOW COLUMNS FROM $tableName LIKE '$columnName'");
                    return $stmt->rowCount() > 0;
                } catch (PDOException $e) {
                    return false;
                }
            };

            // Delete from email_logs (if exists)
            if ($checkTable('email_logs') && $checkColumn('email_logs', 'member_id')) {
                try {
                    $stmt = $pdo->prepare("DELETE FROM email_logs WHERE member_id = ?");
                    $stmt->execute([$member_id]);
                } catch (PDOException $e) {
                    // Continue if error
                }
            }

            // Delete from email_tracking (if exists)
            if ($checkTable('email_tracking') && $checkColumn('email_tracking', 'member_id')) {
                try {
                    $stmt = $pdo->prepare("DELETE FROM email_tracking WHERE member_id = ?");
                    $stmt->execute([$member_id]);
                } catch (PDOException $e) {
                    // Continue if error
                }
            }

            // Handle volunteer opportunities - set contact_person_id to NULL instead of deleting
            if ($checkTable('volunteer_opportunities') && $checkColumn('volunteer_opportunities', 'contact_person_id')) {
                try {
                    $stmt = $pdo->prepare("UPDATE volunteer_opportunities SET contact_person_id = NULL WHERE contact_person_id = ?");
                    $stmt->execute([$member_id]);
                } catch (PDOException $e) {
                    // Continue if error
                }
            }

            // Delete from volunteer_applications if member applied for opportunities
            if ($checkTable('volunteer_applications') && $checkColumn('volunteer_applications', 'member_id')) {
                try {
                    $stmt = $pdo->prepare("DELETE FROM volunteer_applications WHERE member_id = ?");
                    $stmt->execute([$member_id]);
                } catch (PDOException $e) {
                    // Continue if error
                }
            }

            // Delete from event_rsvps if member has RSVPs
            if ($checkTable('event_rsvps') && $checkColumn('event_rsvps', 'member_id')) {
                try {
                    $stmt = $pdo->prepare("DELETE FROM event_rsvps WHERE member_id = ?");
                    $stmt->execute([$member_id]);
                } catch (PDOException $e) {
                    // Continue if error
                }
            }

            // Delete from member_skills if table exists
            if ($checkTable('member_skills') && $checkColumn('member_skills', 'member_id')) {
                try {
                    $stmt = $pdo->prepare("DELETE FROM member_skills WHERE member_id = ?");
                    $stmt->execute([$member_id]);
                } catch (PDOException $e) {
                    // Continue if error
                }
            }

            // Delete from family_relationships if table exists
            if ($checkTable('family_relationships') && $checkColumn('family_relationships', 'member_id')) {
                try {
                    $stmt = $pdo->prepare("DELETE FROM family_relationships WHERE member_id = ? OR related_member_id = ?");
                    $stmt->execute([$member_id, $member_id]);
                } catch (PDOException $e) {
                    // Continue if error
                }
            }

            // Delete from member_gifts if table exists
            if ($checkTable('member_gifts')) {
                try {
                    if ($checkColumn('member_gifts', 'sender_id')) {
                        $stmt = $pdo->prepare("DELETE FROM member_gifts WHERE sender_id = ? OR recipient_id = ?");
                        $stmt->execute([$member_id, $member_id]);
                    } else if ($checkColumn('member_gifts', 'recipient_id')) {
                        $stmt = $pdo->prepare("DELETE FROM member_gifts WHERE recipient_id = ?");
                        $stmt->execute([$member_id]);
                    }
                } catch (PDOException $e) {
                    // Continue if error
                }
            }

            // Delete the member
            $stmt = $pdo->prepare("DELETE FROM members WHERE id = ?");
            $result = $stmt->execute([$member_id]);
            
            if ($result && $stmt->rowCount() > 0) {
                $deletion_results['successfully_deleted']++;
                $deletion_results['deleted_members'][] = [
                    'id' => $member_id,
                    'name' => $member['full_name'],
                    'email' => $member['email']
                ];
                
                // Clean up profile image if exists
                if (!empty($member['image_path'])) {
                    $image_full_path = '../../' . $member['image_path'];
                    if (file_exists($image_full_path)) {
                        if (unlink($image_full_path)) {
                            $deletion_results['files_cleaned']++;
                        }
                    }
                }
                
            } else {
                $deletion_results['failed_deletions'][] = [
                    'id' => $member_id,
                    'name' => $member['full_name'],
                    'email' => $member['email'],
                    'reason' => 'Member not found or already deleted'
                ];
            }
            
        } catch (PDOException $e) {
            $deletion_results['failed_deletions'][] = [
                'id' => $member_id,
                'name' => $member['full_name'],
                'email' => $member['email'],
                'reason' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    // Log the bulk deletion activity
    $log_message = sprintf(
        "Bulk member deletion: %d/%d members successfully deleted by admin ID %d",
        $deletion_results['successfully_deleted'],
        $deletion_results['total_requested'],
        $_SESSION['admin_id']
    );
    
    // Insert activity log
    try {
        $stmt = $pdo->prepare("INSERT INTO admin_activity_logs (admin_id, action, details, ip_address, created_at) VALUES (?, ?, ?, ?, NOW())");
        $stmt->execute([
            $_SESSION['admin_id'],
            'bulk_delete_members',
            json_encode($deletion_results),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ]);
    } catch (PDOException $e) {
        // Log insertion failed, but don't fail the main operation
        error_log("Failed to log bulk member deletion activity: " . $e->getMessage());
    }
    
    $pdo->commit();
    
    // Prepare response
    $response = [
        'success' => true,
        'message' => sprintf(
            'Bulk deletion completed: %d out of %d members successfully deleted',
            $deletion_results['successfully_deleted'],
            $deletion_results['total_requested']
        ),
        'results' => $deletion_results
    ];
    
    // Add file cleanup info
    if ($deletion_results['files_cleaned'] > 0) {
        $response['file_cleanup'] = sprintf('%d profile images cleaned up', $deletion_results['files_cleaned']);
    }
    
    // Add warnings if there were failures
    if (!empty($deletion_results['failed_deletions'])) {
        $response['warnings'] = sprintf(
            '%d members could not be deleted',
            count($deletion_results['failed_deletions'])
        );
    }
    
    echo json_encode($response);
    
} catch (PDOException $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("Bulk member deletion error: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'Database error occurred during bulk deletion',
        'error_details' => $e->getMessage()
    ]);
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("Bulk member deletion error: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'An unexpected error occurred',
        'error_details' => $e->getMessage()
    ]);
}
?>
