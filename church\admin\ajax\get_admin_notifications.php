<?php
require_once '../../config/database.php';
require_once '../includes/session-manager.php';
require_once '../includes/admin_notification_functions.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

$adminId = $_SESSION['user_id'];

try {
    // Get recent notifications for the admin
    $notifications = getAdminNotifications($pdo, $adminId, 10, 0, false);
    
    // Format the notifications with time
    foreach ($notifications as &$notification) {
        $notification['formatted_time'] = formatAdminNotificationTime($notification['created_at']);
        $notification['icon'] = getAdminNotificationIcon($notification['notification_type']);
        $notification['color_class'] = getAdminNotificationColorClass($notification['priority']);
    }
    
    // Get unread count
    $unreadCount = getAdminUnreadNotificationCount($pdo, $adminId);
    
    echo json_encode([
        'success' => true,
        'notifications' => $notifications,
        'unread_count' => $unreadCount
    ]);
    
} catch (Exception $e) {
    error_log("Error getting admin notifications: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Error retrieving notifications'
    ]);
}
?>
