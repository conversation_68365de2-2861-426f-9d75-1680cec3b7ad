<?php
/**
 * Fix Member Deletion Issues
 * This script creates missing email system tables and fixes member deletion functionality
 */

require_once '../config.php';

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Fix Member Deletion Issues</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-md-10'>
            <div class='card'>
                <div class='card-header bg-primary text-white'>
                    <h3 class='mb-0'><i class='bi bi-tools me-2'></i>Fix Member Deletion Issues</h3>
                </div>
                <div class='card-body'>";

try {
    echo "<div class='alert alert-info'>
            <h5><i class='bi bi-info-circle me-2'></i>Diagnosing Member Deletion Issues</h5>
            <p>Checking for missing tables and fixing database structure...</p>
          </div>";

    // Check which tables exist
    $tables_to_check = [
        'email_logs',
        'email_tracking', 
        'email_schedule_logs',
        'email_queue',
        'admin_activity_logs',
        'migration_log'
    ];

    $existing_tables = [];
    $missing_tables = [];

    foreach ($tables_to_check as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            $existing_tables[] = $table;
        } else {
            $missing_tables[] = $table;
        }
    }

    echo "<div class='row mb-4'>
            <div class='col-md-6'>
                <div class='card bg-light'>
                    <div class='card-body'>
                        <h6><i class='bi bi-check-circle text-success me-2'></i>Existing Tables</h6>";
    
    if (!empty($existing_tables)) {
        echo "<ul class='list-unstyled mb-0'>";
        foreach ($existing_tables as $table) {
            echo "<li><i class='bi bi-check text-success me-2'></i>$table</li>";
        }
        echo "</ul>";
    } else {
        echo "<p class='text-muted mb-0'>No email system tables found</p>";
    }

    echo "      </div>
                </div>
            </div>
            <div class='col-md-6'>
                <div class='card bg-light'>
                    <div class='card-body'>
                        <h6><i class='bi bi-exclamation-triangle text-warning me-2'></i>Missing Tables</h6>";

    if (!empty($missing_tables)) {
        echo "<ul class='list-unstyled mb-0'>";
        foreach ($missing_tables as $table) {
            echo "<li><i class='bi bi-x text-danger me-2'></i>$table</li>";
        }
        echo "</ul>";
    } else {
        echo "<p class='text-success mb-0'>All required tables exist</p>";
    }

    echo "      </div>
                </div>
            </div>
          </div>";

    // Create missing tables
    if (!empty($missing_tables)) {
        echo "<div class='alert alert-warning'>
                <h6><i class='bi bi-exclamation-triangle me-2'></i>Creating Missing Tables</h6>
                <p>The following tables are missing and will be created:</p>
                <ul>";
        foreach ($missing_tables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul></div>";

        // Read and execute the SQL file
        $sql_file = __DIR__ . '/sql/email_system_tables.sql';
        if (file_exists($sql_file)) {
            $sql_content = file_get_contents($sql_file);
            
            // Split SQL statements
            $statements = array_filter(array_map('trim', explode(';', $sql_content)));
            
            $created_count = 0;
            foreach ($statements as $statement) {
                if (!empty($statement) && !preg_match('/^--/', $statement)) {
                    try {
                        $pdo->exec($statement);
                        $created_count++;
                    } catch (PDOException $e) {
                        // Log but continue - table might already exist
                        if (strpos($e->getMessage(), 'already exists') === false) {
                            echo "<div class='alert alert-warning'>
                                    <small>Warning executing statement: " . htmlspecialchars($e->getMessage()) . "</small>
                                  </div>";
                        }
                    }
                }
            }
            
            echo "<div class='alert alert-success'>
                    <i class='bi bi-check-circle me-2'></i>Successfully executed $created_count SQL statements
                  </div>";
        } else {
            echo "<div class='alert alert-danger'>
                    <i class='bi bi-exclamation-triangle me-2'></i>SQL file not found: $sql_file
                  </div>";
        }
    }

    // Verify tables now exist
    echo "<h5 class='mt-4'>Verification: Checking Table Structure</h5>";
    
    foreach ($tables_to_check as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            // Check if member_id column exists in tables that should have it
            if (in_array($table, ['email_logs', 'email_tracking'])) {
                $stmt = $pdo->query("SHOW COLUMNS FROM $table LIKE 'member_id'");
                if ($stmt->rowCount() > 0) {
                    echo "<div class='alert alert-success'>
                            <i class='bi bi-check-circle me-2'></i>Table '$table' exists with member_id column
                          </div>";
                } else {
                    echo "<div class='alert alert-danger'>
                            <i class='bi bi-exclamation-triangle me-2'></i>Table '$table' exists but missing member_id column
                          </div>";
                }
            } else {
                echo "<div class='alert alert-success'>
                        <i class='bi bi-check-circle me-2'></i>Table '$table' exists
                      </div>";
            }
        } else {
            echo "<div class='alert alert-danger'>
                    <i class='bi bi-x-circle me-2'></i>Table '$table' still missing
                  </div>";
        }
    }

    // Test member deletion functionality
    echo "<h5 class='mt-4'>Testing Member Deletion Functionality</h5>";
    
    // Check if there are any members to test with
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM members");
    $member_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo "<div class='alert alert-info'>
            <strong>Total members in database:</strong> $member_count
          </div>";

    if ($member_count > 0) {
        // Test the deletion queries without actually deleting
        $test_member_id = 999999; // Use a non-existent ID for testing
        
        $deletion_queries = [
            "DELETE FROM email_logs WHERE member_id = ?",
            "DELETE FROM email_tracking WHERE member_id = ?",
            "UPDATE volunteer_opportunities SET contact_person_id = NULL WHERE contact_person_id = ?",
            "DELETE FROM volunteer_applications WHERE member_id = ?",
            "DELETE FROM event_rsvps WHERE member_id = ?",
            "DELETE FROM member_skills WHERE member_id = ?",
            "DELETE FROM family_relationships WHERE member_id = ? OR related_member_id = ?"
        ];

        $test_results = [];
        foreach ($deletion_queries as $query) {
            try {
                $stmt = $pdo->prepare($query);
                if (strpos($query, 'family_relationships') !== false) {
                    $stmt->execute([$test_member_id, $test_member_id]);
                } else {
                    $stmt->execute([$test_member_id]);
                }
                $test_results[] = ['query' => $query, 'status' => 'success', 'error' => null];
            } catch (PDOException $e) {
                $test_results[] = ['query' => $query, 'status' => 'error', 'error' => $e->getMessage()];
            }
        }

        echo "<div class='table-responsive'>
                <table class='table table-sm'>
                    <thead>
                        <tr>
                            <th>Query</th>
                            <th>Status</th>
                            <th>Error</th>
                        </tr>
                    </thead>
                    <tbody>";

        foreach ($test_results as $result) {
            $status_class = $result['status'] === 'success' ? 'text-success' : 'text-danger';
            $status_icon = $result['status'] === 'success' ? 'bi-check-circle' : 'bi-x-circle';
            
            echo "<tr>
                    <td><code>" . htmlspecialchars($result['query']) . "</code></td>
                    <td class='$status_class'><i class='bi $status_icon me-1'></i>" . ucfirst($result['status']) . "</td>
                    <td>" . ($result['error'] ? "<small class='text-danger'>" . htmlspecialchars($result['error']) . "</small>" : '') . "</td>
                  </tr>";
        }

        echo "    </tbody>
                </table>
              </div>";

        $success_count = count(array_filter($test_results, function($r) { return $r['status'] === 'success'; }));
        $total_count = count($test_results);

        if ($success_count === $total_count) {
            echo "<div class='alert alert-success'>
                    <h6><i class='bi bi-check-circle me-2'></i>All Deletion Queries Working</h6>
                    <p class='mb-0'>All $total_count deletion queries executed successfully. Member deletion should now work properly.</p>
                  </div>";
        } else {
            echo "<div class='alert alert-warning'>
                    <h6><i class='bi bi-exclamation-triangle me-2'></i>Some Issues Remain</h6>
                    <p class='mb-0'>$success_count out of $total_count queries succeeded. Check the errors above for remaining issues.</p>
                  </div>";
        }
    }

    echo "<div class='alert alert-success mt-4'>
            <h6><i class='bi bi-check-circle me-2'></i>Fix Complete</h6>
            <p class='mb-0'>
                The member deletion system has been repaired. Missing email system tables have been created 
                and the deletion functionality should now work properly.
            </p>
          </div>";

} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>
            <h5><i class='bi bi-exclamation-triangle me-2'></i>Database Error</h5>
            <p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
            <p><strong>Code:</strong> " . $e->getCode() . "</p>
          </div>";
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>
            <h5><i class='bi bi-exclamation-triangle me-2'></i>General Error</h5>
            <p>" . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
}

echo "          <div class='mt-4 text-center'>
                    <a href='members.php' class='btn btn-primary me-2'>
                        <i class='bi bi-people me-2'></i>Go to Members
                    </a>
                    <a href='dashboard.php' class='btn btn-secondary'>
                        <i class='bi bi-speedometer2 me-2'></i>Admin Dashboard
                    </a>
                </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>";
?>
