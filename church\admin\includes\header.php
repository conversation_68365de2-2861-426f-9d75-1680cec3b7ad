<?php
// Include session manager for timeout handling
require_once __DIR__ . '/session-manager.php';

// Include language system
require_once __DIR__ . '/language.php';

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: " . admin_url_for('login.php'));
    exit();
}
?>
<!DOCTYPE html>
<html lang="<?php echo get_current_language(); ?>" dir="<?php echo get_language_direction(); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="generator" content="<?php echo get_organization_name(); ?> Management System v1.0">
    <!-- Debug info for path troubleshooting -->
    <meta name="debug-site-url" content="<?php echo SITE_URL; ?>">
    <meta name="debug-admin-url" content="<?php echo ADMIN_URL; ?>">
    <meta name="debug-env" content="<?php echo $environment ?? 'unknown'; ?>">
    <!-- Favicon Support -->
    <?php
    $favicon = get_site_setting('favicon_logo', '');
    if (!empty($favicon)): ?>
        <link rel="icon" type="image/x-icon" href="<?php echo get_base_url() . '/' . htmlspecialchars($favicon); ?>">
        <link rel="shortcut icon" type="image/x-icon" href="<?php echo get_base_url() . '/' . htmlspecialchars($favicon); ?>">
    <?php else: ?>
        <link rel="icon" type="image/x-icon" href="<?php echo get_base_url(); ?>/assets/images/favicon.ico">
    <?php endif; ?>

    <title><?php echo isset($page_title) ? $page_title : 'Admin Panel'; ?> - <?php echo get_site_title(); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome Icons (primary) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Bootstrap Icons (backup) -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Added cache-busting parameter to prevent caching issues -->
    <link rel="stylesheet" href="<?php echo ADMIN_URL; ?>/admin-css-proxy.php?t=<?php echo time(); ?>">
    <link rel="stylesheet" href="<?php echo ADMIN_URL; ?>/css/font-fix.css?t=<?php echo time(); ?>">

    <!-- Custom Theme CSS -->
    <?php
    $customThemeFile = __DIR__ . '/../css/custom-theme.css';
    if (file_exists($customThemeFile)): ?>
        <link rel="stylesheet" href="<?php echo ADMIN_URL; ?>/css/custom-theme.css?t=<?php echo filemtime($customThemeFile); ?>">
    <?php endif; ?>

    <!-- Dark Mode CSS -->
    <link rel="stylesheet" href="<?php echo ADMIN_URL; ?>/css/dark-mode.css?t=<?php echo time(); ?>">

    <!-- Accessibility CSS -->
    <link rel="stylesheet" href="<?php echo ADMIN_URL; ?>/css/accessibility.css?t=<?php echo time(); ?>">

    <?php if (isset($extra_css)): ?>
        <?php echo $extra_css; ?>
    <?php endif; ?>
    
    <!-- Base URL for JavaScript -->
    <script>
        var BASE_URL = '<?php echo get_base_url(); ?>';
        var ADMIN_URL = '<?php echo get_admin_url(); ?>';
        var SITE_URL = '<?php echo SITE_URL; ?>';
    </script>

    <!-- Theme Manager -->
    <script src="<?php echo ADMIN_URL; ?>/js/theme-manager.js?t=<?php echo time(); ?>"></script>

    <!-- Accessibility Manager -->
    <script src="<?php echo ADMIN_URL; ?>/js/accessibility-manager.js?t=<?php echo time(); ?>"></script>

    <!-- Language Switcher -->
    <script src="<?php echo ADMIN_URL; ?>/js/language-switcher.js?t=<?php echo time(); ?>"></script>

    <!-- Mobile Responsive JavaScript -->
    <script src="<?php echo ADMIN_URL; ?>/js/mobile-responsive.js?t=<?php echo time(); ?>"></script>

    <!-- Mobile Navigation Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebarOverlay');
            const sidebarCloseBtn = document.getElementById('sidebarCloseBtn');
            const mobileUserMenu = document.getElementById('mobileUserMenu');

            console.log('Mobile navigation initialized');
            console.log('Elements found:', {
                mobileMenuBtn: !!mobileMenuBtn,
                sidebar: !!sidebar,
                sidebarOverlay: !!sidebarOverlay,
                sidebarCloseBtn: !!sidebarCloseBtn
            });

            function closeSidebar() {
                if (sidebar) sidebar.classList.remove('show');
                if (sidebarOverlay) sidebarOverlay.classList.remove('show');
                document.body.style.overflow = '';
                document.body.style.position = '';
                document.body.style.width = '';
            }

            function openSidebar() {
                if (sidebar) sidebar.classList.add('show');
                if (sidebarOverlay) sidebarOverlay.classList.add('show');
                document.body.style.overflow = 'hidden';
                document.body.style.position = 'fixed';
                document.body.style.width = '100%';
            }

            // Mobile menu toggle
            if (mobileMenuBtn) {
                mobileMenuBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Mobile menu button clicked');

                    if (sidebar && sidebar.classList.contains('show')) {
                        closeSidebar();
                    } else {
                        openSidebar();
                    }
                });
            }

            // Close sidebar when overlay is clicked
            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Overlay clicked');
                    closeSidebar();
                });
            }

            // Close sidebar when close button is clicked
            if (sidebarCloseBtn) {
                sidebarCloseBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Close button clicked');
                    closeSidebar();
                });
            }

            // Close sidebar when escape key is pressed
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && sidebar && sidebar.classList.contains('show')) {
                    console.log('Escape key pressed');
                    closeSidebar();
                }
            });

            // Mobile user menu
            if (mobileUserMenu) {
                mobileUserMenu.addEventListener('click', function() {
                    window.location.href = '<?php echo admin_url_for('profile.php'); ?>';
                });
            }

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 768) {
                    closeSidebar();
                }
            });
        });
    </script>
</head>
<body>
    <!-- Mobile Header -->
    <div class="mobile-header d-md-none" style="display: flex !important;">
        <button class="mobile-menu-btn" id="mobileMenuBtn" type="button" style="background: none; border: none; color: white; font-size: 1.5rem; padding: 8px;">
            <i class="bi bi-list"></i>
        </button>
        <div class="mobile-title" style="flex: 1; text-align: center; color: white; font-weight: 600;">
            <?php echo isset($page_title) ? htmlspecialchars($page_title) : 'Admin Panel'; ?>
        </div>
        <button class="mobile-user-menu" id="mobileUserMenu" type="button" style="background: none; border: none; color: white; font-size: 1.2rem; padding: 8px;">
            <i class="bi bi-person-circle"></i>
        </button>
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div class="sidebar-overlay d-md-none" id="sidebarOverlay"></div>

    <div class="container-fluid">
        <div class="row">
            <!-- Include the sidebar -->
            <?php include dirname(__FILE__) . '/sidebar.php'; ?>

            <!-- Main Content -->
            <div class="main-content">
                <?php if (isset($page_header)): ?>
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h2><?php echo $page_header; ?></h2>
                        <?php if (isset($page_description)): ?>
                            <p><?php echo $page_description; ?></p>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if (isset($message) && !empty($message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($error) && !empty($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?> 