/**
 * Mobile Responsive JavaScript for Admin Panel
 * Handles mobile-specific functionality and enhancements
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize mobile responsive features
    initMobileResponsive();
    
    // Re-initialize on window resize
    window.addEventListener('resize', debounce(initMobileResponsive, 250));
    
    function initMobileResponsive() {
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            // Initialize mobile-specific features
            initMobileTables();
            initMobileCards();
            initMobileForms();
            initMobileModals();
            initMobileTooltips();
        } else {
            // Clean up mobile-specific features for desktop
            cleanupMobileFeatures();
        }
    }
    
    /**
     * Initialize mobile-friendly tables
     */
    function initMobileTables() {
        const tables = document.querySelectorAll('.table');
        
        tables.forEach(table => {
            // Skip if already processed
            if (table.classList.contains('mobile-processed')) return;
            
            // Add mobile stack class
            table.classList.add('table-mobile-stack');
            
            // Add data labels for mobile view
            const headers = table.querySelectorAll('thead th');
            const rows = table.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                cells.forEach((cell, index) => {
                    if (headers[index]) {
                        const headerText = headers[index].textContent.trim();
                        cell.setAttribute('data-label', headerText);
                    }
                });
            });
            
            table.classList.add('mobile-processed');
        });
    }
    
    /**
     * Initialize mobile-friendly cards
     */
    function initMobileCards() {
        const cards = document.querySelectorAll('.card');
        
        cards.forEach(card => {
            // Add touch-friendly interactions
            card.style.cursor = 'default';
            
            // Improve card spacing on mobile
            if (!card.classList.contains('mobile-card-processed')) {
                card.classList.add('mobile-card-processed');
            }
        });
    }
    
    /**
     * Initialize mobile-friendly forms
     */
    function initMobileForms() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            // Skip if already processed
            if (form.classList.contains('mobile-form-processed')) return;
            
            // Improve form inputs for mobile
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                // Prevent zoom on iOS
                if (input.type === 'text' || input.type === 'email' || input.type === 'password' || 
                    input.type === 'number' || input.type === 'tel' || input.tagName === 'TEXTAREA' || 
                    input.tagName === 'SELECT') {
                    input.style.fontSize = '16px';
                }
                
                // Add touch-friendly styling
                input.style.minHeight = '48px';
                input.style.padding = '12px 15px';
            });
            
            // Improve button groups
            const btnGroups = form.querySelectorAll('.btn-group');
            btnGroups.forEach(group => {
                group.style.flexDirection = 'column';
                group.style.width = '100%';
                
                const buttons = group.querySelectorAll('.btn');
                buttons.forEach((btn, index) => {
                    btn.style.width = '100%';
                    btn.style.marginBottom = '8px';
                    btn.style.borderRadius = '8px';
                    if (index === buttons.length - 1) {
                        btn.style.marginBottom = '0';
                    }
                });
            });
            
            form.classList.add('mobile-form-processed');
        });
    }
    
    /**
     * Initialize mobile-friendly modals
     */
    function initMobileModals() {
        const modals = document.querySelectorAll('.modal');
        
        modals.forEach(modal => {
            // Skip if already processed
            if (modal.classList.contains('mobile-modal-processed')) return;
            
            const modalDialog = modal.querySelector('.modal-dialog');
            if (modalDialog) {
                modalDialog.style.margin = '10px';
                modalDialog.style.maxWidth = 'calc(100% - 20px)';
            }
            
            const modalBody = modal.querySelector('.modal-body');
            if (modalBody) {
                modalBody.style.maxHeight = '60vh';
                modalBody.style.overflowY = 'auto';
            }
            
            modal.classList.add('mobile-modal-processed');
        });
    }
    
    /**
     * Initialize mobile-friendly tooltips
     */
    function initMobileTooltips() {
        // Disable tooltips on mobile for better touch experience
        const tooltipElements = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltipElements.forEach(element => {
            element.removeAttribute('data-bs-toggle');
            element.removeAttribute('title');
        });
    }
    
    /**
     * Clean up mobile-specific features when switching to desktop
     */
    function cleanupMobileFeatures() {
        const tables = document.querySelectorAll('.table-mobile-stack');
        tables.forEach(table => {
            table.classList.remove('table-mobile-stack');
        });
    }
    
    /**
     * Debounce function to limit function calls
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    /**
     * Handle mobile navigation
     */
    function initMobileNavigation() {
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const sidebar = document.getElementById('sidebar');
        const sidebarOverlay = document.getElementById('sidebarOverlay');
        
        if (mobileMenuBtn && sidebar && sidebarOverlay) {
            // Ensure proper mobile navigation behavior
            mobileMenuBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                sidebar.classList.toggle('show');
                sidebarOverlay.classList.toggle('show');
                
                // Prevent body scroll when sidebar is open
                if (sidebar.classList.contains('show')) {
                    document.body.style.overflow = 'hidden';
                    document.body.style.position = 'fixed';
                    document.body.style.width = '100%';
                } else {
                    document.body.style.overflow = '';
                    document.body.style.position = '';
                    document.body.style.width = '';
                }
            });
            
            // Close sidebar when clicking outside
            sidebarOverlay.addEventListener('click', function() {
                sidebar.classList.remove('show');
                sidebarOverlay.classList.remove('show');
                document.body.style.overflow = '';
                document.body.style.position = '';
                document.body.style.width = '';
            });
        }
    }
    
    // Initialize mobile navigation
    initMobileNavigation();
    
    /**
     * Handle mobile-specific table actions
     */
    function initMobileTableActions() {
        const actionButtons = document.querySelectorAll('.table .btn-group .btn');
        
        actionButtons.forEach(button => {
            // Increase touch target size
            button.style.minHeight = '44px';
            button.style.minWidth = '44px';
            button.style.padding = '10px 15px';
            
            // Add touch feedback
            button.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.95)';
            });
            
            button.addEventListener('touchend', function() {
                this.style.transform = 'scale(1)';
            });
        });
    }
    
    // Initialize mobile table actions
    initMobileTableActions();
    
    /**
     * Improve mobile form validation display
     */
    function initMobileFormValidation() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            form.addEventListener('submit', function(e) {
                const invalidInputs = form.querySelectorAll(':invalid');
                
                if (invalidInputs.length > 0) {
                    e.preventDefault();
                    
                    // Scroll to first invalid input
                    invalidInputs[0].scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                    
                    // Focus on first invalid input
                    setTimeout(() => {
                        invalidInputs[0].focus();
                    }, 300);
                }
            });
        });
    }
    
    // Initialize mobile form validation
    initMobileFormValidation();
});

/**
 * Utility function to check if device is mobile
 */
function isMobileDevice() {
    return window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

/**
 * Utility function to add mobile-friendly loading states
 */
function showMobileLoading(element) {
    if (isMobileDevice()) {
        element.style.opacity = '0.6';
        element.style.pointerEvents = 'none';
        
        const spinner = document.createElement('div');
        spinner.className = 'mobile-loading-spinner';
        spinner.innerHTML = '<i class="bi bi-arrow-clockwise" style="animation: spin 1s linear infinite;"></i>';
        spinner.style.position = 'absolute';
        spinner.style.top = '50%';
        spinner.style.left = '50%';
        spinner.style.transform = 'translate(-50%, -50%)';
        spinner.style.zIndex = '1000';
        
        element.style.position = 'relative';
        element.appendChild(spinner);
    }
}

function hideMobileLoading(element) {
    if (isMobileDevice()) {
        element.style.opacity = '';
        element.style.pointerEvents = '';
        
        const spinner = element.querySelector('.mobile-loading-spinner');
        if (spinner) {
            spinner.remove();
        }
    }
}
