<?php
/**
 * English Language File
 * Default language for the Organization Management System
 */

return [
    // Common
    'yes' => 'Yes',
    'no' => 'No',
    'save' => 'Save',
    'cancel' => 'Cancel',
    'delete' => 'Delete',
    'edit' => 'Edit',
    'view' => 'View',
    'add' => 'Add',
    'create' => 'Create',
    'update' => 'Update',
    'search' => 'Search',
    'filter' => 'Filter',
    'export' => 'Export',
    'import' => 'Import',
    'print' => 'Print',
    'close' => 'Close',
    'back' => 'Back',
    'check' => 'Check',
    'next' => 'Next',
    'previous' => 'Previous',
    'loading' => 'Loading...',
    'success' => 'Success',
    'error' => 'Error',
    'warning' => 'Warning',
    'info' => 'Information',
    'confirm' => 'Confirm',
    'name' => 'Name',
    'email' => 'Email',
    'phone' => 'Phone',
    'address' => 'Address',
    'date' => 'Date',
    'time' => 'Time',
    'status' => 'Status',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'total' => 'Total',
    'count' => 'Count',
    'actions' => 'Actions',
    'settings' => 'Settings',
    'preferences' => 'Preferences',
    
    // Navigation
    'dashboard' => 'Dashboard',
    'members' => 'Members',
    'events' => 'Events',
    'campaigns' => 'Campaigns',
    'communications' => 'Communications',
    'reports' => 'Reports',
    'settings' => 'Settings',
    'logout' => 'Logout',
    'profile' => 'Profile',
    'home' => 'Home',
    
    // Dashboard
    'welcome_message' => 'Welcome to your {site_name} Dashboard',
    'quick_stats' => 'Quick Statistics',
    'recent_activity' => 'Recent Activity',
    'upcoming_events' => 'Upcoming Events',
    'member_count' => 'Total Members',
    'active_campaigns' => 'Active Campaigns',
    'this_month' => 'This Month',
    'this_week' => 'This Week',
    'today' => 'Today',
    'days' => 'Days',
    'birthdays' => 'Birthdays',
    'calendar' => 'Calendar',
    'no_birthdays_this_month' => 'No birthdays this month.',
    'recent_members' => 'Recent Members',
    'new' => 'New',
    
    // Members
    'add_member' => 'Add Member',
    'edit_member' => 'Edit Member',
    'member_details' => 'Member Details',
    'member_list' => 'Member List',
    'first_name' => 'First Name',
    'last_name' => 'Last Name',
    'full_name' => 'Full Name',
    'phone_number' => 'Phone Number',
    'birth_date' => 'Birth Date',
    'gender' => 'Gender',
    'join_date' => 'Join Date',
    'member_since' => 'Member Since',
    'contact_info' => 'Contact Information',
    'personal_info' => 'Personal Information',
    
    // Events
    'add_event' => 'Add Event',
    'edit_event' => 'Edit Event',
    'event_details' => 'Event Details',
    'event_list' => 'Event List',
    'event_title' => 'Event Title',
    'event_description' => 'Event Description',
    'start_date' => 'Start Date',
    'end_date' => 'End Date',
    'start_time' => 'Start Time',
    'end_time' => 'End Time',
    'location' => 'Location',
    'organizer' => 'Organizer',
    'attendees' => 'Attendees',
    'capacity' => 'Capacity',
    'registration' => 'Registration',
    
    // Communications
    'send_email' => 'Send Email',
    'send_sms' => 'Send SMS',
    'email_campaigns' => 'Email Campaigns',
    'sms_campaigns' => 'SMS Campaigns',
    'bulk_email' => 'Bulk Email',
    'bulk_sms' => 'Bulk SMS',
    'single_sms' => 'Single SMS',
    'email_templates' => 'Email Templates',
    'sms_templates' => 'SMS Templates',
    'recipients' => 'Recipients',
    'subject' => 'Subject',
    'message' => 'Message',
    'send_now' => 'Send Now',
    'schedule_send' => 'Schedule Send',
    'draft' => 'Draft',
    'sent' => 'Sent',
    'delivered' => 'Delivered',
    'failed' => 'Failed',
    
    // Forms
    'required_field' => 'This field is required',
    'invalid_email' => 'Please enter a valid email address',
    'invalid_phone' => 'Please enter a valid phone number',
    'invalid_date' => 'Please enter a valid date',
    'password_mismatch' => 'Passwords do not match',
    'form_saved' => 'Form saved successfully',
    'form_error' => 'There was an error saving the form',
    'confirm_delete' => 'Are you sure you want to delete this item?',
    'delete_success' => 'Item deleted successfully',
    'delete_error' => 'There was an error deleting the item',
    
    // Pagination
    'showing_results' => 'Showing {start} to {end} of {total} results',
    'no_results' => 'No results found',
    'page' => 'Page',
    'of' => 'of',
    'per_page' => 'per page',
    'first' => 'First',
    'last' => 'Last',
    
    // Accessibility
    'skip_to_content' => 'Skip to main content',
    'toggle_navigation' => 'Toggle navigation',
    'toggle_dark_mode' => 'Toggle dark mode',
    'increase_font_size' => 'Increase font size',
    'decrease_font_size' => 'Decrease font size',
    'high_contrast' => 'High contrast',
    'screen_reader_text' => 'Screen reader text',
    
    // Theme and Appearance
    'appearance' => 'Appearance',
    'theme' => 'Theme',
    'light_mode' => 'Light Mode',
    'dark_mode' => 'Dark Mode',
    'auto_mode' => 'Auto (Follow System)',
    'color_scheme' => 'Color Scheme',
    'font_size' => 'Font Size',
    'small' => 'Small',
    'normal' => 'Normal',
    'large' => 'Large',
    'extra_large' => 'Extra Large',
    
    // Language
    'language' => 'Language',
    'select_language' => 'Select Language',
    'language_changed' => 'Language changed successfully',
    
    // Time and Date
    'january' => 'January',
    'february' => 'February',
    'march' => 'March',
    'april' => 'April',
    'may' => 'May',
    'june' => 'June',
    'july' => 'July',
    'august' => 'August',
    'september' => 'September',
    'october' => 'October',
    'november' => 'November',
    'december' => 'December',
    'monday' => 'Monday',
    'tuesday' => 'Tuesday',
    'wednesday' => 'Wednesday',
    'thursday' => 'Thursday',
    'friday' => 'Friday',
    'saturday' => 'Saturday',
    'sunday' => 'Sunday',
    
    // Notifications
    'notification' => 'Notification',
    'notifications' => 'Notifications',
    'mark_as_read' => 'Mark as read',
    'mark_all_read' => 'Mark all as read',
    'no_notifications' => 'No notifications',
    
    // User Management
    'user' => 'User',
    'users' => 'Users',
    'admin' => 'Admin',
    'administrator' => 'Administrator',
    'role' => 'Role',
    'permissions' => 'Permissions',
    'login' => 'Login',
    'password' => 'Password',
    'confirm_password' => 'Confirm Password',
    'change_password' => 'Change Password',
    'forgot_password' => 'Forgot Password',
    'reset_password' => 'Reset Password',
    
    // System
    'system' => 'System',
    'version' => 'Version',
    'database' => 'Database',
    'backup' => 'Backup',
    'maintenance' => 'Maintenance',
    'logs' => 'Logs',
    'cache' => 'Cache',
    'clear_cache' => 'Clear Cache',
    
    // Analytics
    'analytics' => 'Analytics',
    'statistics' => 'Statistics',
    'chart' => 'Chart',
    'graph' => 'Graph',
    'report' => 'Report',
    'data' => 'Data',
    'metrics' => 'Metrics',
    
    // File Management
    'file' => 'File',
    'files' => 'Files',
    'upload' => 'Upload',
    'download' => 'Download',
    'attachment' => 'Attachment',
    'attachments' => 'Attachments',
    'image' => 'Image',
    'document' => 'Document',
    
    // Integration
    'integration' => 'Integration',
    'integrations' => 'Integrations',
    'api' => 'API',
    'webhook' => 'Webhook',
    'sync' => 'Sync',
    'connect' => 'Connect',
    'disconnect' => 'Disconnect',
    
    // Custom Fields
    'custom_field' => 'Custom Field',
    'custom_fields' => 'Custom Fields',
    'field_type' => 'Field Type',
    'field_name' => 'Field Name',
    'field_label' => 'Field Label',
    'field_value' => 'Field Value',
    'text_field' => 'Text Field',
    'number_field' => 'Number Field',
    'date_field' => 'Date Field',
    'dropdown_field' => 'Dropdown Field',
    'checkbox_field' => 'Checkbox Field',
    'textarea_field' => 'Textarea Field',
    
    // Organization
    'organization' => 'Organization',
    'church' => 'Church',
    'ministry' => 'Ministry',
    'group' => 'Group',
    'department' => 'Department',
    'leader' => 'Leader',
    'pastor' => 'Pastor',
    'volunteer' => 'Volunteer',
    
    // Donations
    'donation' => 'Donation',
    'donations' => 'Donations',
    'offering' => 'Offering',
    'tithe' => 'Tithe',
    'amount' => 'Amount',
    'donor' => 'Donor',
    'payment_method' => 'Payment Method',
    'transaction' => 'Transaction',
    
    // Help and Support
    'help' => 'Help',
    'support' => 'Support',
    'documentation' => 'Documentation',
    'tutorial' => 'Tutorial',
    'faq' => 'FAQ',
    'contact_support' => 'Contact Support',
    'user_guide' => 'User Guide',
    
    // Validation Messages
    'validation_required' => 'The {field} field is required.',
    'validation_email' => 'The {field} field must be a valid email address.',
    'validation_min_length' => 'The {field} field must be at least {min} characters.',
    'validation_max_length' => 'The {field} field must not exceed {max} characters.',
    'validation_numeric' => 'The {field} field must be a number.',
    'validation_date' => 'The {field} field must be a valid date.',
    'validation_unique' => 'The {field} field must be unique.',
    
    // Success Messages
    'success_created' => '{item} created successfully.',
    'success_updated' => '{item} updated successfully.',
    'success_deleted' => '{item} deleted successfully.',
    'success_saved' => 'Changes saved successfully.',
    'success_sent' => '{item} sent successfully.',
    
    // Error Messages
    'error_general' => 'An error occurred. Please try again.',
    'error_not_found' => '{item} not found.',
    'error_permission' => 'You do not have permission to perform this action.',
    'error_validation' => 'Please correct the errors below.',
    'error_database' => 'Database error occurred.',
    'error_network' => 'Network error occurred.',
    
    // Confirmation Messages
    'confirm_delete_item' => 'Are you sure you want to delete this {item}?',
    'confirm_action' => 'Are you sure you want to perform this action?',
    'action_cannot_undone' => 'This action cannot be undone.',

    // SMS System
    'sms_system' => 'SMS System',
    'sms_analytics' => 'SMS Analytics',
    'sms_sent' => 'SMS Sent',
    'sms_delivered' => 'SMS Delivered',
    'sms_failed' => 'SMS Failed',
    'phone_numbers' => 'Phone Numbers',
    'external_contacts' => 'External Contacts',
    'church_members' => 'Members',
    'custom_numbers' => 'Custom Numbers',
    'select_recipients' => 'Select Recipients',
    'enter_phone_numbers' => 'Enter phone numbers (one per line)',
    'sms_message' => 'SMS Message',
    'character_count' => 'Character Count',
    'characters_remaining' => 'characters remaining',
    'send_sms' => 'Send SMS',
    'sms_sent_successfully' => 'SMS sent successfully',
    'sms_send_failed' => 'Failed to send SMS',

    // Email System
    'email_analytics' => 'Email Analytics',
    'emails_sent' => 'Emails Sent',
    'open_rate' => 'Open Rate',
    'click_rate' => 'Click Rate',
    'bounce_rate' => 'Bounce Rate',
    'unsubscribe_rate' => 'Unsubscribe Rate',
    'campaign_performance' => 'Campaign Performance',
    'recipient_analytics' => 'Recipient Analytics',
    'ab_testing' => 'A/B Testing',
    'test_variant' => 'Test Variant',
    'control_group' => 'Control Group',
    'winner' => 'Winner',
    'statistical_significance' => 'Statistical Significance',

    // Sidebar Navigation
    'dashboard' => 'Dashboard',
    'member_management' => 'Member Management',
    'events_management' => 'Events Management',
    'email_management' => 'Email Management',
    'sms_management' => 'SMS Management',
    'integrations' => 'Integrations',
    'account' => 'Account',

    // Member Management
    'members' => 'Members',
    'add_member' => 'Add Member',

    // Events Management
    'events' => 'Events',
    'event_attendance' => 'Event Attendance',
    'event_categories' => 'Event Categories',
    'event_categories_description' => 'Manage categories for organizing events.',
    'event_reports' => 'Event Reports',

    // Email Management
    'email_scheduler' => 'Email Scheduler',
    'contacts' => 'Contacts',
    'contact_groups' => 'Contact Groups',
    'birthday_messages' => 'Birthday Messages',
    'send_birthday_emails' => 'Send Birthday Emails',
    'test_birthday_emails' => 'Test Birthday Emails',
    'debug_placeholders' => 'Debug Placeholders',
    'birthday_notifications' => 'Birthday Notifications',
    'automated_templates' => 'Automated Templates',
    'whatsapp_templates' => 'WhatsApp Templates',
    'whatsapp_messages' => 'WhatsApp Messages',
    'about_shortcodes' => 'About & Shortcodes',

    // SMS Management
    'sms_analytics' => 'SMS Analytics',

    // Integrations
    'calendar_integration' => 'Calendar Integration',
    'social_media' => 'Social Media',
    'sms_integration' => 'SMS Integration',
    'payment_integration' => 'Payment Integration',
    'payment_tables' => 'Payment Tables',
    'donations' => 'Donations',
    'check_payment_sdks' => 'Check Payment SDKs',

    // Account Settings
    'settings' => 'Settings',
    'appearance' => 'Appearance',
    'custom_fields' => 'Custom Fields',
    'logo_upload' => 'Logo Upload',
    'branding' => 'Branding',
    'logo_management' => 'Logo Management',
    'security_setup' => 'Security Setup',
    'security_audit' => 'Security Audit',
    'security_settings' => 'Security Settings',
    'my_profile' => 'My Profile',
    'logout' => 'Logout',

    // Admin pages common translations
    'manage_members_description' => 'Manage members and their information.',
    'managing_members' => 'Managing Members',
    'members_page_description' => 'This page allows you to manage your members database.',
    'add_members' => 'Add Members',
    'add_members_description' => 'Add new members to your database',
    'view_details' => 'View Details',
    'view_details_description' => 'Click on a member\'s name to view their complete profile',
    'edit_information' => 'Edit Information',
    'edit_information_description' => 'Update member details as needed',
    'birthday_communications' => 'Birthday Communications',
    'birthday_communications_description' => 'Send birthday messages from the member details page',
    'tip' => 'Tip',
    'search_tip_description' => 'Use the search box to quickly find specific members by name, email, or phone number.',
    'search_members_placeholder' => 'Search members...',
    'search_by_name_email_phone' => 'Search by name, email, or phone number',
    'add_new_member_tooltip' => 'Add a new member to the database',
    'id' => 'ID',
    'no_data_available' => 'N/A',
    'no_members_found' => 'No members found.',
    'confirm_delete_member_message' => 'Are you sure you want to delete',
    'action_cannot_be_undone' => 'This action cannot be undone.',
    'invalid_security_token' => 'Invalid security token. Please try again.',
    'member_deleted_successfully' => 'Member deleted successfully!',
    'error_deleting_member' => 'Error deleting member',

    // Events page translations
    'managing_events' => 'Managing Events',
    'events_page_description' => 'This page allows you to manage your events.',
    'add_events' => 'Add Events',
    'add_events_description' => 'Create new events for your organization',
    'edit_events' => 'Edit Events',
    'edit_events_description' => 'Update event details as needed',
    'event_attendance_tracking' => 'Event Attendance Tracking',
    'event_attendance_description' => 'Track attendance for your events',
    'search_events_placeholder' => 'Search events...',
    'search_by_title_description' => 'Search by event title or description',
    'add_new_event_tooltip' => 'Create a new event',
    'no_events_found' => 'No events found.',
    'event_deleted_successfully' => 'Event deleted successfully!',
    'error_deleting_event' => 'Error deleting event',

    // Settings page translations
    'general_settings' => 'General Settings',
    'notification_settings' => 'Notification Settings',
    'backup_settings' => 'Backup Settings',
    'settings_saved_successfully' => 'Settings saved successfully!',
    'error_saving_settings' => 'Error saving settings',

    // Form elements
    'form_required_field' => 'This field is required',
    'form_invalid_email' => 'Please enter a valid email address',
    'form_invalid_phone' => 'Please enter a valid phone number',
    'form_invalid_date' => 'Please enter a valid date',
    'form_password_mismatch' => 'Passwords do not match',
    'form_password_too_short' => 'Password must be at least 8 characters',

    // Bulk actions
    'bulk_actions' => 'Bulk Actions',
    'select_items_first' => 'Please select items first',
    'bulk_delete_confirmation' => 'Are you sure you want to delete the selected items?',
    'bulk_operation_completed' => 'Bulk operation completed successfully',
    'bulk_operation_failed' => 'Bulk operation failed',

    // File operations
    'file_upload_success' => 'File uploaded successfully',
    'file_upload_failed' => 'File upload failed',
    'file_too_large' => 'File is too large',
    'invalid_file_type' => 'Invalid file type',
    'file_not_found' => 'File not found',

    // Pagination
    'showing_entries' => 'Showing {start} to {end} of {total} entries',
    'previous_page' => 'Previous',
    'next_page' => 'Next',
    'first_page' => 'First',
    'last_page' => 'Last',
    'go_to_page' => 'Go to page',
    'items_per_page' => 'Items per page',

    // Modal dialogs
    'modal_confirm_title' => 'Confirm Action',
    'modal_warning_title' => 'Warning',
    'modal_error_title' => 'Error',
    'modal_success_title' => 'Success',
    'modal_info_title' => 'Information',

    // Navigation
    'breadcrumb_home' => 'Home',
    'breadcrumb_admin' => 'Admin',
    'go_back' => 'Go Back',
    'return_to_list' => 'Return to List',

    // Time and dates
    'created_on' => 'Created on',
    'updated_on' => 'Updated on',
    'last_modified' => 'Last modified',
    'never' => 'Never',
    'just_now' => 'Just now',
    'minutes_ago' => '{count} minutes ago',
    'hours_ago' => '{count} hours ago',
    'days_ago' => '{count} days ago',

    // Status indicators
    'status_active' => 'Active',
    'status_inactive' => 'Inactive',
    'status_pending' => 'Pending',
    'status_completed' => 'Completed',
    'status_cancelled' => 'Cancelled',
    'status_draft' => 'Draft',
    'status_published' => 'Published',

    // Bulk delete modal translations
    'confirm_bulk_member_deletion' => 'Confirm Bulk Member Deletion',
    'danger_action_cannot_be_undone' => 'DANGER: This action cannot be undone!',
    'bulk_delete_warning_message' => 'You are about to permanently delete the selected members and all their associated data including profile images.',
    'selected_members' => 'Selected Members',
    'what_will_be_deleted' => 'What will be deleted',
    'member_records' => 'Member records',
    'profile_images' => 'Profile images',
    'email_logs_tracking_data' => 'Email logs and tracking data',
    'all_associated_data' => 'All associated data',
    'understand_permanent_action' => 'I understand this action is permanent and cannot be undone',
    'type_delete_to_confirm' => 'Type',
    'type_delete_placeholder' => 'Type DELETE here',
    'delete_selected_members' => 'Delete Selected Members',

    // Events management translations
    'events_management' => 'Events Management',
    'events_management_description' => 'Create, manage, and track events and activities.',
    'create_event' => 'Create Event',
    'manage_categories' => 'Manage Categories',
    'attendance_tracking' => 'Attendance Tracking',
    'view_public_calendar' => 'View Public Calendar',
    'search_events' => 'Search Events',
    'search_events_placeholder' => 'Search by title, description, or location',
    'all_categories' => 'All Categories',

    // Settings page translations
    'general_settings' => 'General Settings',
    'setting_updated_successfully' => 'Setting updated successfully!',
    'error_loading_settings' => 'Error loading settings',
    'security_settings' => 'Security Settings',
    'organization' => 'Organization',
    'system' => 'System',
    'notifications' => 'Notifications',
    'integrations' => 'Integrations',

    // Organization settings
    'site_title' => 'Site Title',
    'organization_name' => 'Organization Name',
    'organization_type' => 'Organization Type',
    'admin_panel_title' => 'Admin Panel Title',
    'member_term' => 'Member Term',
    'leader_term' => 'Leader Term',
    'group_term' => 'Group Term',
    'event_term' => 'Event Term',
    'donation_term' => 'Donation Term',

    // System settings
    'timezone' => 'Timezone',
    'date_format' => 'Date Format',
    'time_format' => 'Time Format',
    'currency_symbol' => 'Currency Symbol',
    'currency_code' => 'Currency Code',
    'language' => 'Language',
    'items_per_page' => 'Items Per Page',
    'session_timeout_seconds' => 'Session Timeout (seconds)',
    'max_upload_size_mb' => 'Max Upload Size (MB)',
    'backup_retention_days' => 'Backup Retention (days)',

    // Notification settings
    'birthday_notification_days' => 'Birthday Notification Days',
    'event_reminder_days' => 'Event Reminder Days',
    'membership_expiry_days' => 'Membership Expiry Days',
    'notification_frequency' => 'Notification Frequency',

    // Integration settings
    'payment_gateway' => 'Payment Gateway',
    'google_analytics_id' => 'Google Analytics ID',
    'facebook_pixel_id' => 'Facebook Pixel ID',

    // Additional settings translations
    'settings' => 'Settings',
    'church' => 'Church',
    'school' => 'School',
    'business' => 'Business',
    'nonprofit' => 'Non-Profit',
    'organization_option' => 'Organization',
    'english' => 'English',
    'spanish' => 'Spanish',
    'enter' => 'Enter',
    'update' => 'Update',
    'related_settings' => 'Related Settings',
    'appearance_settings' => 'Appearance Settings',
    'email_settings' => 'Email Settings',
    'security_settings_description' => 'Password policies, login security, 2FA',
    'appearance_settings_description' => 'Colors, themes, branding',
    'email_settings_description' => 'SMTP configuration, email templates',

    // Add member page translations
    'add_new_member' => 'Add New Member',
    'add_new_member_description' => 'Add a new member to the database.',
    'member_information' => 'Member Information',
    'full_name' => 'Full Name',
    'email_address' => 'Email Address',
    'phone_number' => 'Phone Number',
    'phone_number_help' => 'Include country code (e.g., +27 for South Africa)',
    'gender' => 'Gender',
    'occupation' => 'Occupation',
    'home_address' => 'Home Address',
    'birth_date' => 'Birth Date',
    'profile_image' => 'Profile Image',
    'prayer_requests_comments' => 'Prayer Requests or Comments',
    'back_to_members' => 'Back to Members',
    'add_member' => 'Add Member',

    // Validation messages
    'full_name_required' => 'Full name is required',
    'email_required' => 'Email address is required',
    'valid_email_required' => 'Please enter a valid email address',
    'birth_date_required' => 'Birth date is required',
    'birth_date_format_error' => 'Birth date must be in YYYY-MM-DD format',
    'please_correct_following' => 'Please correct the following',
    'email_already_registered' => 'This email address is already registered.',

    // Success and error messages
    'member_added_successfully' => 'Member added successfully!',
    'member_added_email_sent' => 'Member added successfully! A welcome email has been sent to their email address.',
    'welcome_email_failed' => 'The welcome email could not be sent',
    'welcome_email_failed_check_settings' => 'The welcome email could not be sent. Please check your email settings.',
    'error_creating_user_account' => 'Error creating user account',
    'failed_create_upload_directory' => 'Failed to create uploads directory',
    'image_upload_warning' => 'Warning: Failed to upload image, but member will be added without profile image.',

    // Events page translations
    'event_created_successfully' => 'Event created successfully!',
    'error_creating_event' => 'Error creating event',
    'event_updated_successfully' => 'Event updated successfully!',
    'error_updating_event' => 'Error updating event',
    'event_deleted_successfully' => 'Event deleted successfully!',
    'error_deleting_event' => 'Error deleting event',
    'all_statuses' => 'All Statuses',
    'draft' => 'Draft',
    'published' => 'Published',
    'cancelled' => 'Cancelled',
    'completed' => 'Completed',
    'filter' => 'Filter',
    'events' => 'Events',
    'total' => 'total',
    'no_events_found' => 'No Events Found',
    'create_first_event' => 'Create your first event to get started.',
    'event' => 'Event',
    'date_time' => 'Date & Time',
    'rsvps' => 'RSVPs',
    'actions' => 'Actions',
    'files' => 'file(s)',
    'tbd' => 'TBD',
    'uncategorized' => 'Uncategorized',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'event_title' => 'Event Title',
    'select_category' => 'Select Category',
    'start_date_time' => 'Start Date & Time',
    'end_date_time' => 'End Date & Time',
    'event_location_placeholder' => 'Event location or address',
    'optional' => 'Optional',
    'requirements_notes' => 'Requirements/Notes',
    'requirements_placeholder' => 'Any special requirements or notes for attendees',
    'event_files_pdf' => 'Event Files (PDF)',
    'upload_pdf_files_help' => 'Upload PDF files related to this event (programs, flyers, etc.)',
    'registration_deadline' => 'Registration Deadline',
    'public_event' => 'Public Event',
    'visible_public_calendar' => 'Visible on public calendar',
    'registration_required' => 'Registration Required',
    'require_rsvp_attend' => 'Require RSVP to attend',
    'confirm_delete' => 'Confirm Delete',
    'delete_event_confirmation' => 'Are you sure you want to delete the event',
    'action_cannot_undone' => 'This action cannot be undone.',
    'delete_event' => 'Delete Event',
    'event_details' => 'Event Details',
    'registration' => 'Registration',
    'visibility' => 'Visibility',
    'event_files' => 'Event Files',
    'edit_event' => 'Edit Event',

    // Edit member page translations
    'failed_upload_image' => 'Failed to upload image',
    'invalid_file_type_image' => 'Invalid file type. Please upload JPG, JPEG, PNG, or GIF files only',
    'email_already_exists_another_member' => 'Email address already exists for another member',
    'member_updated_successfully' => 'Member information updated successfully',
    'error_updating_member' => 'Error updating member',
    'edit_member' => 'Edit Member',
    'update_member_info_description' => 'Update member information in the database.',
    'back_to_member' => 'Back to Member',
    'current_profile_photo' => 'Current Profile Photo',
    'current_profile_image' => 'Current profile image',
    'remove_image' => 'Remove Image',
    'no_current_image' => 'No current image',
    'upload_image_help_text' => 'Upload JPG, JPEG, PNG, or GIF files only. Leave empty to keep current image.',
    'new_image_preview' => 'New image preview',
    'update_member' => 'Update Member',
    'confirm_remove_profile_image' => 'Are you sure you want to remove the current profile image?',
    'image_will_be_removed' => 'Image will be removed when you save',

    // View member page translations
    'view_member' => 'View Member',
    'member_details' => 'Member Details',
    'call' => 'Call',
    'emails_sent' => 'Emails Sent',
    'last_email_opened' => 'Last Email Opened',
    'today_is_birthday' => 'Today is their birthday!',
    'birthday_tomorrow' => 'Birthday is tomorrow!',
    'birthday_in_days' => 'Birthday in %d days!',
    'next_birthday' => 'Next Birthday',
    'age_on_next_birthday' => 'Age on Next Birthday',
    'quick_actions' => 'Quick Actions',
    'send_birthday_message' => 'Send Birthday Message',
    'delete_member' => 'Delete Member',
    'member_information' => 'Member Information',
    'phone' => 'Phone',
    'not_provided' => 'Not provided',
    'age' => 'Age',
    'confirm_delete_member_message' => 'Are you sure you want to delete %s? This action cannot be undone.',
    'delete' => 'Delete',
    'address' => 'Address',
    'joined' => 'Joined',
    'last_updated' => 'Last Updated',
    'not_available' => 'Not available',
    'year' => 'year',
    'years' => 'years',
    'month' => 'month',
    'months' => 'months',
    'day' => 'day',
    'days' => 'days',
    'ago' => 'ago',
    'notes' => 'Notes',
    'message_from_member' => 'Message from Member',
    'communication_history' => 'Communication History',
    'date' => 'Date',
    'subject' => 'Subject',
    'template' => 'Template',
    'type' => 'Type',
    'status' => 'Status',
    'unknown' => 'Unknown',
    'standard' => 'Standard',
    'birthday' => 'Birthday',
    'reminder' => 'Reminder',
    'notification' => 'Notification',
    'welcome' => 'Welcome',
    'sent' => 'Sent',
    'failed' => 'Failed',
    'unknown_error' => 'Unknown error',
    'no_communication_history' => 'No communication history found.',

    // Contacts page
    'contact_management' => 'Contact Management',
    'manage_external_contacts_description' => 'Manage external contacts for bulk email communications.',
    'only_csv_txt_files_allowed' => 'Only CSV and TXT files are allowed.',
    'successfully_imported_contacts' => 'Successfully imported %d contacts.',
    'failed_import_entries' => 'Failed to import %d entries.',
    'error_processing_file' => 'Error processing file',
    'error_fetching_groups' => 'Error fetching groups',
    'error_fetching_statistics' => 'Error fetching statistics',
    'invalid_security_token' => 'Invalid security token. Please try again.',
    'contact_deleted_successfully' => 'Contact deleted successfully!',
    'failed_delete_contact' => 'Failed to delete contact.',
    'error_deleting_contact' => 'Error deleting contact',
    'total_contacts' => 'Total Contacts',
    'total_groups' => 'Total Groups',
    'ungrouped_contacts' => 'Ungrouped Contacts',
    'recent_contacts' => 'Recent Contacts',
    'last_30_days' => 'Last 30 days',
    'import_contacts' => 'Import Contacts',
    'contact_list' => 'Contact List',
    'showing_contacts_range' => 'Showing %d to %d of %s contacts',
    'create_contact_group' => 'Create Contact Group',
    'edit_contact' => 'Edit Contact',
    'confirm_delete_contact_message' => 'Are you sure you want to delete',
    'action_cannot_be_undone' => 'This action cannot be undone.',
    'confirm_bulk_deletion' => 'Confirm Bulk Deletion',
    'danger_action_cannot_be_undone' => 'DANGER: This action cannot be undone!',
    'bulk_delete_warning_message' => 'You are about to permanently delete the selected contacts and all their associated data.',
    'selected_contacts' => 'Selected Contacts',
    'what_will_be_deleted' => 'What will be deleted',
    'contact_records' => 'Contact records',
    'group_memberships' => 'Group memberships',
    'email_logs_tracking_data' => 'Email logs and tracking data',
    'all_associated_data' => 'All associated data',

    // Email templates page
    'email_templates' => 'Email Templates',
    'manage_email_templates_description' => 'Manage email templates for birthday messages and other communications.',
    'cannot_delete_template_in_use' => 'Cannot delete template as it is referenced in the email logs.',
    'template_deleted_successfully' => 'Template deleted successfully!',
    'error_deleting_template' => 'Error deleting template',
    'about_email_templates' => 'About Email Templates',
    'email_templates_help_description' => 'Email templates help you send consistent, professional communications to your members.',
    'birthday_templates' => 'Birthday Templates',
    'birthday_templates_description' => 'Used for automated birthday greetings',
    'bulk_email_templates' => 'Bulk Email Templates',
    'bulk_email_templates_description' => 'Used for newsletters and announcements',
    'reminder_templates' => 'Reminder Templates',
    'reminder_templates_description' => 'Used for follow-ups and special notices',
    'note' => 'Note',
    'newsletter_templates_note' => 'Newsletter templates will not include member profile images to maintain a consistent appearance.',
    'search_templates_placeholder' => 'Search templates...',
    'search_templates_tooltip' => 'Search by template name or content',
    'clear' => 'Clear',
    'create_new_template' => 'Create New Template',
    'create_template_tooltip' => 'Create a new email template for birthdays, newsletters, or other communications',
    'showing_search_results' => 'Showing %d results for "%s"',
    'template_name' => 'Template Name',
    'bulk' => 'Bulk',
    'general' => 'General',
    'no_templates_found' => 'No templates found.',
    'create_first_template' => 'Create your first template!',
    'confirm_delete_template_message' => 'Are you sure you want to delete this template? This action cannot be undone.',

    // Bulk email page
    'bulk_email' => 'Bulk Email',
    'send_bulk_emails_description' => 'Send bulk emails to selected members.',
    'send_bulk_email' => 'Send Bulk Email',
    'email_throttling_settings' => 'Email Throttling Settings',
    'how_to_send_bulk_emails' => 'How to Send Bulk Emails',
    'select_recipients' => 'Select Recipients',
    'sending_bulk_email' => 'Sending Bulk Email',

    // Donations page
    'donation_status_updated_successfully' => 'Donation status updated successfully.',
    'error_updating_donation_status' => 'Error updating donation status',
    'invalid_donation_id_or_status' => 'Invalid donation ID or status.',
    'manage_donations' => 'Manage Donations',
    'view_manage_donations_description' => 'View and manage all donations made through the website.',
    'donation_summary' => 'Donation Summary',
    'total_donations' => 'Total Donations',
    'total_amount' => 'Total Amount',
    'status_breakdown' => 'Status Breakdown',

    // Appearance settings page
    'setting_updated_successfully' => 'Setting updated successfully!',
    'all_appearance_settings_saved_successfully' => 'All appearance settings saved successfully!',
    'appearance_settings' => 'Appearance Settings',
    'customize_appearance_description' => 'Customize the appearance and styling of your website.',
    'advanced_theme_customizer' => 'Advanced Theme Customizer',
    'color_scheme' => 'Color Scheme',
    'typography' => 'Typography',
    'layout_settings' => 'Layout Settings',
    'sidebar_colors' => 'Sidebar Colors',
    'logo_and_branding' => 'Logo and Branding',
    'theme_settings' => 'Theme Settings',
    'custom_css' => 'Custom CSS',

    // Security settings page
    'security_settings' => 'Security Settings',
    'configure_security_settings_description' => 'Configure advanced security settings for your management system.',
    'security_configuration' => 'Security Configuration',
    'password_policy' => 'Password Policy',
    'login_security' => 'Login Security',
    'system_security' => 'System Security',
    'security_information' => 'Security Information',

    // Email settings page
    'email_settings' => 'Email Settings',
    'configure_email_settings_description' => 'Configure your email settings for sending messages to members.',
    'smtp_configuration' => 'SMTP Configuration',
    'test_email_configuration' => 'Test Email Configuration',
    'help_and_information' => 'Help & Information',
    'common_smtp_settings' => 'Common SMTP Settings',

    // Birthday management page
    'birthday_management' => 'Birthday Management',
    'manage_birthday_communications_description' => 'Manage birthday communications for members.',
    'birthday_management_guide' => 'Birthday Management Guide',
    'birthday_celebrants_month' => 'Birthday Celebrants (%s)',
    'send_birthday_message' => 'Send Birthday Message',
    'sending_birthday_message' => 'Sending a Birthday Message',
    'send_whatsapp_birthday_message' => 'Send WhatsApp Birthday Message',
    'message_preview' => 'Message Preview',

    // Profile page
    'my_profile' => 'My Profile',
    'view_manage_admin_account_description' => 'View and manage your admin account information.',
    'profile_information' => 'Profile Information',
    'change_password' => 'Change Password',
    'two_factor_authentication' => 'Two-Factor Authentication',
    'two_factor_authentication_setup' => 'Two-Factor Authentication Setup',
    'your_backup_codes' => 'Your Backup Codes',

    // Contact groups page
    'contact_groups' => 'Contact Groups',
    'manage_contact_groups_description' => 'Manage contact groups and their members.',
    'create_new_group' => 'Create New Group',
    'confirm_action' => 'Confirm Action',
    'upload_contacts_to_group' => 'Upload Contacts to %s',
    'members_of_group' => 'Members of %s',
    'add_contacts_to_group' => 'Add Contacts to %s',

    // Enhanced donation system
    'enhanced_donation_system' => 'Enhanced Donation System',
    'support_freedom_assembly_church_with_flexible_giving_options' => 'Support Freedom Assembly Church with flexible giving options',
    'select_donation_type' => 'Select Donation Type',
    'general_donation' => 'General Donation',
    'support_our_mission_and_ministry' => 'Support our mission and ministry',
    'birthday_gift' => 'Birthday Gift',
    'send_a_special_gift_to_celebrate_someones_birthday' => 'Send a special gift to celebrate someone\'s birthday',
    'type' => 'Type',
    'details' => 'Details',
    'amount' => 'Amount',
    'payment' => 'Payment',
    'next' => 'Next',
];
?>
