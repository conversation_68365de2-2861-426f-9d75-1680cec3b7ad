<?php
/**
 * Migration script to add gender column to members table
 * Run this script once to add the gender field to the database
 */

require_once '../config.php';

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Migration - Add Gender Column</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-md-8'>
            <div class='card'>
                <div class='card-header'>
                    <h3 class='mb-0'>Database Migration: Add Gender Column</h3>
                </div>
                <div class='card-body'>";

try {
    echo "<div class='alert alert-info'>Starting migration process...</div>";
    
    // Check if gender column already exists
    $stmt = $pdo->query("SHOW COLUMNS FROM members LIKE 'gender'");
    $columnExists = $stmt->rowCount() > 0;
    
    if ($columnExists) {
        echo "<div class='alert alert-warning'>
                <i class='bi bi-exclamation-triangle'></i> 
                Gender column already exists in the members table. No migration needed.
              </div>";
    } else {
        echo "<div class='alert alert-info'>Gender column not found. Adding gender column...</div>";
        
        // Add gender column
        $pdo->exec("ALTER TABLE members 
                   ADD COLUMN gender ENUM('Male', 'Female', 'Other', 'Prefer not to say') DEFAULT NULL 
                   AFTER birth_date");
        
        echo "<div class='alert alert-success'>✓ Gender column added successfully</div>";
        
        // Add index for better performance
        try {
            $pdo->exec("CREATE INDEX idx_gender ON members(gender)");
            echo "<div class='alert alert-success'>✓ Index created for gender column</div>";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "<div class='alert alert-info'>Index for gender column already exists</div>";
            } else {
                throw $e;
            }
        }
    }
    
    // Create migration log table if it doesn't exist
    $pdo->exec("CREATE TABLE IF NOT EXISTS migration_log (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        migration_name VARCHAR(255) UNIQUE NOT NULL,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        description TEXT,
        INDEX idx_migration_name (migration_name),
        INDEX idx_executed_at (executed_at)
    )");
    
    // Log the migration
    $stmt = $pdo->prepare("INSERT INTO migration_log (migration_name, executed_at, description) 
                          VALUES (?, NOW(), ?) 
                          ON DUPLICATE KEY UPDATE executed_at = NOW()");
    $stmt->execute([
        'add_gender_column', 
        'Added gender column to members table with ENUM values: Male, Female, Other, Prefer not to say'
    ]);
    
    echo "<div class='alert alert-success'>✓ Migration logged successfully</div>";
    
    // Check current table structure
    echo "<h5 class='mt-4'>Current Members Table Structure:</h5>";
    $stmt = $pdo->query("DESCRIBE members");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='table-responsive'>
            <table class='table table-sm table-striped'>
                <thead>
                    <tr>
                        <th>Field</th>
                        <th>Type</th>
                        <th>Null</th>
                        <th>Key</th>
                        <th>Default</th>
                        <th>Extra</th>
                    </tr>
                </thead>
                <tbody>";
    
    foreach ($columns as $column) {
        $highlight = ($column['Field'] === 'gender') ? 'table-success' : '';
        echo "<tr class='{$highlight}'>
                <td><strong>{$column['Field']}</strong></td>
                <td>{$column['Type']}</td>
                <td>{$column['Null']}</td>
                <td>{$column['Key']}</td>
                <td>{$column['Default']}</td>
                <td>{$column['Extra']}</td>
              </tr>";
    }
    
    echo "</tbody></table></div>";
    
    // Count existing members
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM members");
    $memberCount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo "<div class='alert alert-info mt-3'>
            <strong>Total members in database:</strong> {$memberCount}<br>
            <small>Existing members will have NULL gender values until they update their profiles.</small>
          </div>";
    
    echo "<div class='alert alert-success mt-4'>
            <h5><i class='bi bi-check-circle'></i> Migration Completed Successfully!</h5>
            <p class='mb-0'>The gender column has been added to the members table. You can now:</p>
            <ul class='mt-2 mb-0'>
                <li>Use the updated registration form with gender field</li>
                <li>Use the updated admin add member form</li>
                <li>Allow existing members to update their gender in their profiles</li>
            </ul>
          </div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>
            <h5><i class='bi bi-exclamation-triangle'></i> Database Error</h5>
            <p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
            <p><strong>Code:</strong> " . $e->getCode() . "</p>
          </div>";
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>
            <h5><i class='bi bi-exclamation-triangle'></i> General Error</h5>
            <p>" . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
}

echo "          <div class='mt-4'>
                    <a href='members.php' class='btn btn-primary'>Go to Members</a>
                    <a href='add_member.php' class='btn btn-secondary'>Add New Member</a>
                    <a href='../register.php' class='btn btn-outline-primary'>Test Registration Form</a>
                </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>";
?>
