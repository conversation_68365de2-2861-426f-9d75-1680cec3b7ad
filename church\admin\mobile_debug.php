<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Set page variables
$page_title = 'Mobile Debug';
$page_header = 'Mobile Debug Test';
$page_description = 'Debug page for mobile responsive issues';

// Include header
include 'includes/header.php';
?>

<style>
/* Debug styles to see what's happening */
.debug-info {
    background: #f8f9fa;
    border: 2px solid #007bff;
    padding: 15px;
    margin: 10px 0;
    border-radius: 5px;
}

.mobile-header {
    background: linear-gradient(135deg, #007bff, #6c757d) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: 56px !important;
    z-index: 1030 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 0 16px !important;
}

@media (max-width: 768px) {
    .mobile-header {
        display: flex !important;
    }
    
    .main-content {
        margin-top: 56px !important;
        padding: 16px !important;
    }
}
</style>

<div class="debug-info">
    <h4>Mobile Debug Information</h4>
    <p><strong>Screen Width:</strong> <span id="screenWidth"></span>px</p>
    <p><strong>Mobile Header Visible:</strong> <span id="mobileHeaderVisible"></span></p>
    <p><strong>Sidebar State:</strong> <span id="sidebarState"></span></p>
    <p><strong>Elements Found:</strong></p>
    <ul id="elementsList"></ul>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>Test Mobile Features</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-primary" onclick="testMobileMenu()">Test Mobile Menu</button>
                <button class="btn btn-secondary" onclick="debugElements()">Debug Elements</button>
                <button class="btn btn-info" onclick="checkCSS()">Check CSS</button>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="stat-card">
            <div class="d-flex align-items-center">
                <div class="icon-container bg-primary p-3 rounded-circle me-3">
                    <i class="bi bi-people fs-3 text-white"></i>
                </div>
                <div>
                    <h3 class="stat-value mb-0">150</h3>
                    <span class="stat-label text-muted">Total Members</span>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="stat-card">
            <div class="d-flex align-items-center">
                <div class="icon-container bg-warning p-3 rounded-circle me-3">
                    <i class="bi bi-gift fs-3 text-dark"></i>
                </div>
                <div>
                    <h3 class="stat-value mb-0">8</h3>
                    <span class="stat-label text-muted">Birthdays</span>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateDebugInfo() {
    document.getElementById('screenWidth').textContent = window.innerWidth;
    
    const mobileHeader = document.querySelector('.mobile-header');
    document.getElementById('mobileHeaderVisible').textContent = mobileHeader ? 'Yes' : 'No';
    
    const sidebar = document.getElementById('sidebar');
    document.getElementById('sidebarState').textContent = sidebar ? 
        (sidebar.classList.contains('show') ? 'Open' : 'Closed') : 'Not Found';
    
    const elementsList = document.getElementById('elementsList');
    elementsList.innerHTML = '';
    
    const elements = [
        'mobileMenuBtn',
        'sidebar', 
        'sidebarOverlay',
        'sidebarCloseBtn'
    ];
    
    elements.forEach(id => {
        const element = document.getElementById(id);
        const li = document.createElement('li');
        li.textContent = `${id}: ${element ? 'Found' : 'Not Found'}`;
        li.style.color = element ? 'green' : 'red';
        elementsList.appendChild(li);
    });
}

function testMobileMenu() {
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    if (mobileMenuBtn) {
        console.log('Triggering mobile menu click');
        mobileMenuBtn.click();
    } else {
        alert('Mobile menu button not found!');
    }
}

function debugElements() {
    console.log('=== DEBUG ELEMENTS ===');
    console.log('Mobile Menu Button:', document.getElementById('mobileMenuBtn'));
    console.log('Sidebar:', document.getElementById('sidebar'));
    console.log('Sidebar Overlay:', document.getElementById('sidebarOverlay'));
    console.log('Sidebar Close Button:', document.getElementById('sidebarCloseBtn'));
    console.log('Mobile Header:', document.querySelector('.mobile-header'));
    
    const sidebar = document.getElementById('sidebar');
    if (sidebar) {
        console.log('Sidebar classes:', sidebar.className);
        console.log('Sidebar style:', sidebar.style.cssText);
    }
}

function checkCSS() {
    const mobileHeader = document.querySelector('.mobile-header');
    if (mobileHeader) {
        const styles = window.getComputedStyle(mobileHeader);
        console.log('Mobile Header Computed Styles:');
        console.log('Display:', styles.display);
        console.log('Position:', styles.position);
        console.log('Z-Index:', styles.zIndex);
        console.log('Background:', styles.background);
    }
    
    const sidebar = document.getElementById('sidebar');
    if (sidebar) {
        const styles = window.getComputedStyle(sidebar);
        console.log('Sidebar Computed Styles:');
        console.log('Position:', styles.position);
        console.log('Left:', styles.left);
        console.log('Width:', styles.width);
        console.log('Z-Index:', styles.zIndex);
    }
}

// Update debug info on load and resize
document.addEventListener('DOMContentLoaded', updateDebugInfo);
window.addEventListener('resize', updateDebugInfo);

// Check if mobile menu is working
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        if (mobileMenuBtn) {
            console.log('Mobile menu button found and ready');
        } else {
            console.error('Mobile menu button NOT found');
        }
    }, 1000);
});
</script>

<?php include 'includes/footer.php'; ?>
