<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Set page variables
$page_title = 'Mobile Responsive Test';
$page_header = 'Mobile Responsive Test';
$page_description = 'Test page to demonstrate mobile responsive admin features';

// Include header
include 'includes/header.php';
?>

<!-- Test Content -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-info">
            <h5><i class="bi bi-info-circle me-2"></i>Mobile Responsive Test Page</h5>
            <p class="mb-0">This page demonstrates the mobile responsive features of the admin panel. Resize your browser or view on a mobile device to see the responsive behavior.</p>
        </div>
    </div>
</div>

<!-- Stats Cards Row -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stat-card">
            <div class="stat-value">150</div>
            <div class="stat-label">Total Members</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stat-card">
            <div class="stat-value">25</div>
            <div class="stat-label">Events</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stat-card">
            <div class="stat-value">8</div>
            <div class="stat-label">Birthdays</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stat-card">
            <div class="stat-value">42</div>
            <div class="stat-label">Messages</div>
        </div>
    </div>
</div>

<!-- Sample Table -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Sample Data Table</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>John Doe</td>
                                <td><EMAIL></td>
                                <td>****** 567 8900</td>
                                <td><span class="badge bg-success">Active</span></td>
                                <td>
                                    <div class="btn-group">
                                        <button class="btn btn-sm btn-primary">Edit</button>
                                        <button class="btn btn-sm btn-danger">Delete</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>Jane Smith</td>
                                <td><EMAIL></td>
                                <td>****** 567 8901</td>
                                <td><span class="badge bg-warning">Pending</span></td>
                                <td>
                                    <div class="btn-group">
                                        <button class="btn btn-sm btn-primary">Edit</button>
                                        <button class="btn btn-sm btn-danger">Delete</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>Bob Johnson</td>
                                <td><EMAIL></td>
                                <td>****** 567 8902</td>
                                <td><span class="badge bg-success">Active</span></td>
                                <td>
                                    <div class="btn-group">
                                        <button class="btn btn-sm btn-primary">Edit</button>
                                        <button class="btn btn-sm btn-danger">Delete</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sample Form -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Sample Form</h5>
            </div>
            <div class="card-body">
                <form>
                    <div class="mb-3">
                        <label for="name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="name" placeholder="Enter full name">
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="email" placeholder="Enter email">
                    </div>
                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="tel" class="form-control" id="phone" placeholder="Enter phone">
                    </div>
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status">
                            <option value="">Select status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="pending">Pending</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" rows="3" placeholder="Enter notes"></textarea>
                    </div>
                    <div class="btn-group w-100">
                        <button type="submit" class="btn btn-primary">Save</button>
                        <button type="button" class="btn btn-secondary">Cancel</button>
                        <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#testModal">Open Modal</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Mobile Features</h5>
            </div>
            <div class="card-body">
                <h6>Mobile Responsive Features:</h6>
                <ul class="list-unstyled">
                    <li><i class="bi bi-check-circle text-success me-2"></i>Collapsible sidebar with overlay</li>
                    <li><i class="bi bi-check-circle text-success me-2"></i>Mobile-friendly navigation</li>
                    <li><i class="bi bi-check-circle text-success me-2"></i>Touch-optimized buttons (48px min)</li>
                    <li><i class="bi bi-check-circle text-success me-2"></i>Responsive tables with stacking</li>
                    <li><i class="bi bi-check-circle text-success me-2"></i>Mobile-first form design</li>
                    <li><i class="bi bi-check-circle text-success me-2"></i>Improved modal sizing</li>
                    <li><i class="bi bi-check-circle text-success me-2"></i>Enhanced typography scaling</li>
                    <li><i class="bi bi-check-circle text-success me-2"></i>iOS zoom prevention</li>
                </ul>
                
                <h6 class="mt-4">Breakpoints:</h6>
                <ul class="list-unstyled">
                    <li><strong>Mobile:</strong> ≤ 768px</li>
                    <li><strong>Tablet:</strong> 769px - 1366px</li>
                    <li><strong>Desktop:</strong> ≥ 1367px</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Test Modal -->
<div class="modal fade" id="testModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Test Modal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>This is a test modal to demonstrate mobile-responsive modal behavior.</p>
                <div class="mb-3">
                    <label for="modalInput" class="form-label">Sample Input</label>
                    <input type="text" class="form-control" id="modalInput" placeholder="Test input">
                </div>
                <div class="mb-3">
                    <label for="modalSelect" class="form-label">Sample Select</label>
                    <select class="form-select" id="modalSelect">
                        <option value="">Choose option</option>
                        <option value="1">Option 1</option>
                        <option value="2">Option 2</option>
                        <option value="3">Option 3</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Instructions -->
<div class="row">
    <div class="col-12">
        <div class="alert alert-primary">
            <h6><i class="bi bi-lightbulb me-2"></i>Testing Instructions</h6>
            <ol class="mb-0">
                <li>Resize your browser window to test different breakpoints</li>
                <li>Use browser developer tools to simulate mobile devices</li>
                <li>Test the sidebar navigation on mobile (hamburger menu)</li>
                <li>Check table responsiveness - tables should stack on mobile</li>
                <li>Test form inputs - they should be touch-friendly</li>
                <li>Open the modal to test mobile modal behavior</li>
            </ol>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
