<?php
require_once '../config.php';
require_once 'includes/session-manager.php';
require_once 'includes/admin_notification_functions.php';

// Check if user is admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$adminId = $_SESSION['admin_id'];
$message = '';

// Use $conn if $pdo is not available
$db = isset($pdo) ? $pdo : $conn;

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'mark_read':
                if (isset($_POST['notification_id'])) {
                    if (markAdminNotificationAsRead($db, $_POST['notification_id'], $adminId)) {
                        $message = 'Notification marked as read.';
                    }
                }
                break;

            case 'mark_all_read':
                if (markAllAdminNotificationsAsRead($db, $adminId)) {
                    $message = 'All notifications marked as read.';
                }
                break;
        }
    }
}

// Pagination
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// Get total count
$stmt = $db->prepare("
    SELECT COUNT(*)
    FROM admin_notifications
    WHERE recipient_id = ?
    AND (expires_at IS NULL OR expires_at > NOW())
");
$stmt->execute([$adminId]);
$totalNotifications = $stmt->fetchColumn();
$totalPages = ceil($totalNotifications / $limit);

// Get notifications with pagination
$notifications = getAdminNotifications($db, $adminId, $limit, $offset);

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="bi bi-bell"></i> Admin Notifications</h1>
                <div>
                    <?php if ($totalNotifications > 0): ?>
                        <form method="post" class="d-inline">
                            <input type="hidden" name="action" value="mark_all_read">
                            <button type="submit" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-check-all"></i> Mark All as Read
                            </button>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
            
            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if (empty($notifications)): ?>
                <div class="text-center py-5">
                    <i class="bi bi-bell-slash display-1 text-muted"></i>
                    <h4 class="mt-3 text-muted">No Notifications</h4>
                    <p class="text-muted">You don't have any notifications at the moment.</p>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($notifications as $notification): ?>
                        <div class="col-12 mb-3">
                            <div class="card notification-card <?php echo $notification['is_read'] ? '' : 'border-primary'; ?> <?php echo $notification['priority'] !== 'normal' ? 'priority-' . $notification['priority'] : ''; ?>">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="notification-icon me-3 p-3 rounded-circle bg-light <?php echo getAdminNotificationColorClass($notification['priority']); ?>">
                                            <i class="bi <?php echo getAdminNotificationIcon($notification['notification_type']); ?> fs-4"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="card-title mb-1">
                                                        <?php echo htmlspecialchars($notification['title']); ?>
                                                        <?php if (!$notification['is_read']): ?>
                                                            <span class="badge bg-primary ms-2">New</span>
                                                        <?php endif; ?>
                                                        <?php if ($notification['priority'] === 'urgent'): ?>
                                                            <span class="badge bg-danger ms-2">Urgent</span>
                                                        <?php elseif ($notification['priority'] === 'high'): ?>
                                                            <span class="badge bg-warning ms-2">High</span>
                                                        <?php endif; ?>
                                                    </h6>
                                                    <p class="card-text text-muted mb-2"><?php echo nl2br(htmlspecialchars($notification['message'])); ?></p>
                                                    <small class="text-muted">
                                                        <i class="bi bi-clock me-1"></i>
                                                        <?php echo formatAdminNotificationTime($notification['created_at']); ?>
                                                        <?php if ($notification['sender_name']): ?>
                                                            • From: <?php echo htmlspecialchars($notification['sender_name']); ?>
                                                        <?php endif; ?>
                                                    </small>
                                                </div>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                        <i class="bi bi-three-dots"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <?php if (!$notification['is_read']): ?>
                                                            <li>
                                                                <form method="post" class="d-inline">
                                                                    <input type="hidden" name="action" value="mark_read">
                                                                    <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                                                    <button type="submit" class="dropdown-item">
                                                                        <i class="bi bi-check me-2"></i>Mark as Read
                                                                    </button>
                                                                </form>
                                                            </li>
                                                        <?php endif; ?>
                                                        <?php if ($notification['action_url']): ?>
                                                            <li>
                                                                <a class="dropdown-item" href="<?php echo htmlspecialchars($notification['action_url']); ?>">
                                                                    <i class="bi bi-arrow-right me-2"></i>View Details
                                                                </a>
                                                            </li>
                                                        <?php endif; ?>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <nav aria-label="Notifications pagination">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page - 1; ?>">Previous</a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page + 1; ?>">Next</a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
        </main>
    </div>
</div>

<style>
.notification-card {
    transition: all 0.3s ease;
}

.notification-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.notification-card.priority-high {
    border-left: 4px solid #ffc107;
}

.notification-card.priority-urgent {
    border-left: 4px solid #dc3545;
}

.notification-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>

<?php include 'includes/footer.php'; ?>
