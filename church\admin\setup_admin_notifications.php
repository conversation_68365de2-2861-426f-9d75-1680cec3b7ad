<?php
require_once '../config/database.php';
require_once 'includes/session-manager.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: login.php');
    exit();
}

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Setting up Admin Notification System</h2>\n";
    
    // Create admin_notifications table
    echo "<h3>Creating admin_notifications table...</h3>\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS admin_notifications (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            recipient_id INT(11) NOT NULL,
            sender_id INT(11) NULL,
            sender_type ENUM('admin', 'member', 'system') NOT NULL DEFAULT 'system',
            notification_type ENUM('announcement', 'message', 'member_activity', 'system', 'security', 'donation', 'event', 'email', 'error') NOT NULL DEFAULT 'system',
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            action_url VARCHAR(500) NULL,
            is_read TINYINT(1) NOT NULL DEFAULT 0,
            priority ENUM('low', 'normal', 'high', 'urgent') NOT NULL DEFAULT 'normal',
            expires_at DATETIME NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            read_at TIMESTAMP NULL,
            INDEX idx_recipient_read (recipient_id, is_read),
            INDEX idx_created_at (created_at),
            INDEX idx_sender (sender_id, sender_type),
            INDEX idx_type (notification_type),
            INDEX idx_priority (priority)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    echo "✓ Admin notifications table created\n";
    
    // Create admin_notification_preferences table
    echo "<h3>Creating admin_notification_preferences table...</h3>\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS admin_notification_preferences (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            admin_id INT(11) NOT NULL,
            notification_type ENUM('announcement', 'message', 'member_activity', 'system', 'security', 'donation', 'event', 'email', 'error') NOT NULL,
            email_enabled TINYINT(1) NOT NULL DEFAULT 1,
            web_enabled TINYINT(1) NOT NULL DEFAULT 1,
            desktop_enabled TINYINT(1) NOT NULL DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_admin_type (admin_id, notification_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    echo "✓ Admin notification preferences table created\n";
    
    // Get all admin users
    $stmt = $pdo->query("SELECT id FROM admins");
    $adminIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!empty($adminIds)) {
        // Insert default notification preferences for existing admins
        echo "<h3>Setting up default notification preferences...</h3>\n";
        $notificationTypes = ['announcement', 'message', 'member_activity', 'system', 'security', 'donation', 'event', 'email', 'error'];
        
        foreach ($adminIds as $adminId) {
            foreach ($notificationTypes as $type) {
                $pdo->prepare("
                    INSERT IGNORE INTO admin_notification_preferences 
                    (admin_id, notification_type, email_enabled, web_enabled, desktop_enabled)
                    VALUES (?, ?, 1, 1, 1)
                ")->execute([$adminId, $type]);
            }
        }
        echo "✓ Default notification preferences set for " . count($adminIds) . " admins\n";
        
        // Create some sample notifications for testing
        echo "<h3>Creating sample notifications...</h3>\n";
        foreach ($adminIds as $adminId) {
            // Welcome notification
            $pdo->prepare("
                INSERT INTO admin_notifications (recipient_id, sender_type, notification_type, title, message, priority)
                VALUES (?, 'system', 'system', 'Admin Notification System Activated', 'The admin notification system has been successfully set up. You will now receive important notifications about system activities, member actions, and administrative tasks.', 'normal')
            ")->execute([$adminId]);
            
            // System notification
            $pdo->prepare("
                INSERT INTO admin_notifications (recipient_id, sender_type, notification_type, title, message, priority)
                VALUES (?, 'system', 'announcement', 'System Enhancement', 'New features have been added to the admin panel including enhanced member management and communication tracking.', 'low')
            ")->execute([$adminId]);
        }
        echo "✓ Sample notifications created\n";
    }
    
    echo "\n<h3>✅ Admin Notification System Setup Complete!</h3>\n";
    echo "<p>You can now:</p>\n";
    echo "<ul>\n";
    echo "<li>View notifications in the admin header</li>\n";
    echo "<li>Manage notification preferences</li>\n";
    echo "<li>Receive real-time notifications for admin activities</li>\n";
    echo "</ul>\n";
    echo "<p><a href='dashboard.php' class='btn btn-primary'>Go to Dashboard</a></p>\n";
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
?>
