-- Migration script to add gender column to members table
-- This script safely adds the gender column if it doesn't already exist

-- Add gender column to members table
ALTER TABLE members 
ADD COLUMN IF NOT EXISTS gender ENUM('Male', 'Female', 'Other', 'Prefer not to say') DEFAULT NULL 
AFTER birth_date;

-- Add index for gender column for better query performance
CREATE INDEX IF NOT EXISTS idx_gender ON members(gender);

-- Update any existing records to have NULL gender (will be updated manually by admin or during profile updates)
-- No default value is set to allow existing members to update their information

-- Log the migration
INSERT INTO migration_log (migration_name, executed_at, description) 
VALUES ('add_gender_column', NOW(), 'Added gender column to members table with ENUM values: Male, Female, Other, Prefer not to say')
ON DUPLICATE KEY UPDATE executed_at = NOW();

-- Create migration_log table if it doesn't exist
CREATE TABLE IF NOT EXISTS migration_log (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    migration_name VARCHAR(255) UNIQUE NOT NULL,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    description TEXT,
    INDEX idx_migration_name (migration_name),
    INDEX idx_executed_at (executed_at)
);
