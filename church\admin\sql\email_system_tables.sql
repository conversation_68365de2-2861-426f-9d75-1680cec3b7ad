-- Email System Tables
-- This file creates all necessary tables for the email system

-- Create email_logs table
CREATE TABLE IF NOT EXISTS email_logs (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    member_id INT(11) DEFAULT NULL,
    template_id INT(11) DEFAULT NULL,
    email_schedule_id INT(11) DEFAULT NULL,
    email_type VARCHAR(50) DEFAULT 'general',
    subject VARCHAR(500) DEFAULT NULL,
    recipient_email VARCHAR(255) DEFAULT NULL,
    recipient_name VARCHAR(255) DEFAULT NULL,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('pending', 'sent', 'failed', 'cancelled') DEFAULT 'sent',
    error_message TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_member_id (member_id),
    INDEX idx_template_id (template_id),
    INDEX idx_email_schedule_id (email_schedule_id),
    INDEX idx_email_type (email_type),
    INDEX idx_status (status),
    INDEX idx_sent_at (sent_at),
    INDEX idx_recipient_email (recipient_email)
);

-- Create email_tracking table
CREATE TABLE IF NOT EXISTS email_tracking (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    tracking_id VARCHAR(255) NOT NULL UNIQUE,
    member_id INT(11) DEFAULT NULL,
    email_type VARCHAR(50) DEFAULT 'general',
    recipient_email VARCHAR(255) DEFAULT NULL,
    recipient_name VARCHAR(255) DEFAULT NULL,
    subject VARCHAR(500) DEFAULT NULL,
    email_content TEXT DEFAULT NULL,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    opened_at TIMESTAMP NULL DEFAULT NULL,
    opened_count INT(11) DEFAULT 0,
    user_agent TEXT DEFAULT NULL,
    ip_address VARCHAR(45) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tracking_id (tracking_id),
    INDEX idx_member_id (member_id),
    INDEX idx_email_type (email_type),
    INDEX idx_recipient_email (recipient_email),
    INDEX idx_sent_at (sent_at),
    INDEX idx_opened_at (opened_at)
);

-- Create email_schedule_logs table
CREATE TABLE IF NOT EXISTS email_schedule_logs (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    schedule_id INT(11) NOT NULL,
    log_type ENUM('info', 'warning', 'error', 'success') DEFAULT 'info',
    message TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_schedule_id (schedule_id),
    INDEX idx_log_type (log_type),
    INDEX idx_created_at (created_at)
);

-- Create email_queue table if it doesn't exist
CREATE TABLE IF NOT EXISTS email_queue (
    id INT AUTO_INCREMENT PRIMARY KEY,
    recipient_email VARCHAR(255) NOT NULL,
    recipient_name VARCHAR(255),
    subject VARCHAR(500) NOT NULL,
    body TEXT NOT NULL,
    sender_email VARCHAR(255),
    sender_name VARCHAR(255),
    priority INT DEFAULT 5,
    status ENUM('pending', 'sent', 'failed', 'cancelled') DEFAULT 'pending',
    attempts INT DEFAULT 0,
    max_attempts INT DEFAULT 3,
    scheduled_at DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sent_at DATETIME NULL,
    error_message TEXT NULL,
    email_type VARCHAR(50) DEFAULT 'general',
    template_id INT NULL,
    member_id INT NULL,
    INDEX idx_status (status),
    INDEX idx_scheduled (scheduled_at),
    INDEX idx_priority (priority),
    INDEX idx_member_id (member_id),
    INDEX idx_template_id (template_id),
    INDEX idx_created_at (created_at)
);

-- Create admin_activity_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS admin_activity_logs (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    admin_id INT(11) NOT NULL,
    action VARCHAR(100) NOT NULL,
    details TEXT DEFAULT NULL,
    ip_address VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_admin_id (admin_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);

-- Insert initial log entry
INSERT INTO migration_log (migration_name, executed_at, description) 
VALUES ('create_email_system_tables', NOW(), 'Created email_logs, email_tracking, email_schedule_logs, email_queue, and admin_activity_logs tables')
ON DUPLICATE KEY UPDATE executed_at = NOW();

-- Create migration_log table if it doesn't exist
CREATE TABLE IF NOT EXISTS migration_log (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    migration_name VARCHAR(255) UNIQUE NOT NULL,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    description TEXT,
    INDEX idx_migration_name (migration_name),
    INDEX idx_executed_at (executed_at)
);
