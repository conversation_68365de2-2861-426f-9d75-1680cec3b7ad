<?php
require_once '../config.php';
require_once 'includes/session-manager.php';
require_once 'includes/admin_notification_functions.php';

// Check if user is admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

echo "<h2>Test Member Activity Notifications</h2>";

if (isset($_POST['test_request'])) {
    // Simulate a member posting a request
    try {
        // Get a test member
        $stmt = $pdo->prepare("SELECT id, full_name FROM members LIMIT 1");
        $stmt->execute();
        $testMember = $stmt->fetch();
        
        if ($testMember) {
            // Get all admin IDs
            $stmt = $pdo->prepare("SELECT id FROM admins WHERE status = 'active'");
            $stmt->execute();
            $adminIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $notificationTitle = "Test: New Member Request Posted";
            $notificationMessage = $testMember['full_name'] . " has posted a new request: \"Test Prayer Request\" in category: Prayer";
            $actionUrl = "../admin/requests.php";
            
            $successCount = 0;
            foreach ($adminIds as $adminId) {
                $result = createAdminNotification(
                    $pdo,
                    $adminId,
                    $notificationTitle,
                    $notificationMessage,
                    'member_request',
                    $testMember['id'],
                    'member',
                    $actionUrl,
                    'normal'
                );
                if ($result) $successCount++;
            }
            
            echo "<p style='color: green;'>✅ Created $successCount test notifications for member request</p>";
        } else {
            echo "<p style='color: red;'>❌ No test member found</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    }
}

if (isset($_POST['test_skill'])) {
    // Simulate a member adding a skill
    try {
        // Get a test member
        $stmt = $pdo->prepare("SELECT id, full_name FROM members LIMIT 1");
        $stmt->execute();
        $testMember = $stmt->fetch();
        
        if ($testMember) {
            // Get all admin IDs
            $stmt = $pdo->prepare("SELECT id FROM admins WHERE status = 'active'");
            $stmt->execute();
            $adminIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $notificationTitle = "Test: Member Added New Skill";
            $notificationMessage = $testMember['full_name'] . " has added \"Web Development\" to their skills profile in category: Technology";
            $actionUrl = "../admin/member_skills.php?member_id=" . $testMember['id'];
            
            $successCount = 0;
            foreach ($adminIds as $adminId) {
                $result = createAdminNotification(
                    $pdo,
                    $adminId,
                    $notificationTitle,
                    $notificationMessage,
                    'member_skill',
                    $testMember['id'],
                    'member',
                    $actionUrl,
                    'low'
                );
                if ($result) $successCount++;
            }
            
            echo "<p style='color: green;'>✅ Created $successCount test notifications for member skill</p>";
        } else {
            echo "<p style='color: red;'>❌ No test member found</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    }
}

if (isset($_POST['test_volunteer'])) {
    // Simulate a member applying for volunteer opportunity
    try {
        // Get a test member
        $stmt = $pdo->prepare("SELECT id, full_name FROM members LIMIT 1");
        $stmt->execute();
        $testMember = $stmt->fetch();
        
        if ($testMember) {
            // Get all admin IDs
            $stmt = $pdo->prepare("SELECT id FROM admins WHERE status = 'active'");
            $stmt->execute();
            $adminIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $notificationTitle = "Test: New Volunteer Application";
            $notificationMessage = $testMember['full_name'] . " has applied for volunteer opportunity: \"Church Cleanup\"";
            $actionUrl = "../admin/volunteer_opportunities.php";
            
            $successCount = 0;
            foreach ($adminIds as $adminId) {
                $result = createAdminNotification(
                    $pdo,
                    $adminId,
                    $notificationTitle,
                    $notificationMessage,
                    'member_volunteer',
                    $testMember['id'],
                    'member',
                    $actionUrl,
                    'normal'
                );
                if ($result) $successCount++;
            }
            
            echo "<p style='color: green;'>✅ Created $successCount test notifications for volunteer application</p>";
        } else {
            echo "<p style='color: red;'>❌ No test member found</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    }
}

if (isset($_POST['test_comment'])) {
    // Simulate a member commenting on a request
    try {
        // Get a test member
        $stmt = $pdo->prepare("SELECT id, full_name FROM members LIMIT 1");
        $stmt->execute();
        $testMember = $stmt->fetch();
        
        if ($testMember) {
            // Get all admin IDs
            $stmt = $pdo->prepare("SELECT id FROM admins WHERE status = 'active'");
            $stmt->execute();
            $adminIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $notificationTitle = "Test: New Comment on Request";
            $notificationMessage = $testMember['full_name'] . " commented on request: \"Test Prayer Request\"";
            $actionUrl = "../admin/requests.php";
            
            $successCount = 0;
            foreach ($adminIds as $adminId) {
                $result = createAdminNotification(
                    $pdo,
                    $adminId,
                    $notificationTitle,
                    $notificationMessage,
                    'member_comment',
                    $testMember['id'],
                    'member',
                    $actionUrl,
                    'normal'
                );
                if ($result) $successCount++;
            }
            
            echo "<p style='color: green;'>✅ Created $successCount test notifications for member comment</p>";
        } else {
            echo "<p style='color: red;'>❌ No test member found</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    }
}

echo "<h3>Test Member Activity Notifications:</h3>";
echo "<form method='post' style='margin: 10px 0;'>";
echo "<button type='submit' name='test_request' style='padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; margin: 5px;'>Test Member Request</button>";
echo "<button type='submit' name='test_skill' style='padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 4px; margin: 5px;'>Test Member Skill</button>";
echo "<button type='submit' name='test_volunteer' style='padding: 10px 20px; background: #ffc107; color: black; border: none; border-radius: 4px; margin: 5px;'>Test Volunteer Application</button>";
echo "<button type='submit' name='test_comment' style='padding: 10px 20px; background: #17a2b8; color: white; border: none; border-radius: 4px; margin: 5px;'>Test Member Comment</button>";
echo "</form>";

echo "<h3>Current Admin Notifications:</h3>";
try {
    $stmt = $pdo->prepare("SELECT * FROM admin_notifications ORDER BY created_at DESC LIMIT 10");
    $stmt->execute();
    $notifications = $stmt->fetchAll();
    
    if (!empty($notifications)) {
        echo "<table border='1' style='width: 100%; border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Message</th><th>Type</th><th>Priority</th><th>Read</th><th>Created</th></tr>";
        foreach ($notifications as $notification) {
            echo "<tr>";
            echo "<td>" . $notification['id'] . "</td>";
            echo "<td>" . htmlspecialchars($notification['title']) . "</td>";
            echo "<td>" . htmlspecialchars(substr($notification['message'], 0, 50)) . "...</td>";
            echo "<td>" . $notification['notification_type'] . "</td>";
            echo "<td>" . $notification['priority'] . "</td>";
            echo "<td>" . ($notification['is_read'] ? 'Yes' : 'No') . "</td>";
            echo "<td>" . $notification['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No notifications found.</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error loading notifications: " . $e->getMessage() . "</p>";
}

echo "<h3>Navigation:</h3>";
echo "<p><a href='notifications.php'>View All Notifications</a> | <a href='dashboard.php'>Back to Dashboard</a></p>";
?>
