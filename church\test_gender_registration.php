<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Gender Registration System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <h3 class="mb-0">Gender Registration System Test</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h5><i class="bi bi-info-circle me-2"></i>Testing Instructions</h5>
                            <p class="mb-0">This page helps you test the new gender field implementation. Follow the steps below to verify everything is working correctly.</p>
                        </div>

                        <h5>Step 1: Database Migration</h5>
                        <div class="mb-4">
                            <p>First, run the database migration to add the gender column:</p>
                            <a href="admin/migrate_gender_column.php" class="btn btn-primary" target="_blank">
                                <i class="bi bi-database me-2"></i>Run Database Migration
                            </a>
                            <small class="form-text text-muted d-block mt-2">
                                This will add the gender column to the members table if it doesn't exist.
                            </small>
                        </div>

                        <h5>Step 2: Test Registration Form</h5>
                        <div class="mb-4">
                            <p>Test the updated registration form with the new field order and gender field:</p>
                            <a href="register.php" class="btn btn-success" target="_blank">
                                <i class="bi bi-person-plus me-2"></i>Test Registration Form
                            </a>
                            <small class="form-text text-muted d-block mt-2">
                                Check that fields are in the correct order: Full Name → Birth Date → Gender → Email → Phone → Profile Image → Occupation → Physical Address → Password → Confirm Password → Requests/Comments
                            </small>
                        </div>

                        <h5>Step 3: Test Admin Add Member</h5>
                        <div class="mb-4">
                            <p>Test the admin add member form with the gender field:</p>
                            <a href="admin/add_member.php" class="btn btn-warning" target="_blank">
                                <i class="bi bi-person-plus-fill me-2"></i>Test Admin Add Member
                            </a>
                            <small class="form-text text-muted d-block mt-2">
                                Login to admin panel first, then test adding a member with the gender field.
                            </small>
                        </div>

                        <h5>Step 4: Verify Database Structure</h5>
                        <div class="mb-4">
                            <p>Check the current database structure:</p>
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6>Expected Members Table Structure:</h6>
                                    <ul class="list-unstyled">
                                        <li><code>id</code> - Primary key</li>
                                        <li><code>full_name</code> - VARCHAR</li>
                                        <li><code>first_name</code> - VARCHAR</li>
                                        <li><code>last_name</code> - VARCHAR</li>
                                        <li><code>birth_date</code> - DATE</li>
                                        <li><strong><code>gender</code> - ENUM('Male', 'Female', 'Other', 'Prefer not to say')</strong> ← NEW</li>
                                        <li><code>email</code> - VARCHAR</li>
                                        <li><code>phone_number</code> - VARCHAR</li>
                                        <li><code>image_path</code> - VARCHAR</li>
                                        <li><code>occupation</code> - VARCHAR</li>
                                        <li><code>home_address</code> - TEXT</li>
                                        <li><code>password_hash</code> - VARCHAR</li>
                                        <li>... other fields</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <h5>Step 5: Test Form Validation</h5>
                        <div class="mb-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6>Validation Tests to Perform:</h6>
                                    <ul>
                                        <li>✅ Try submitting registration form without selecting gender</li>
                                        <li>✅ Try submitting with invalid gender value</li>
                                        <li>✅ Verify all gender options are available</li>
                                        <li>✅ Check that form maintains field order</li>
                                        <li>✅ Test mobile responsiveness</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <h5>Changes Made Summary</h5>
                        <div class="mb-4">
                            <div class="accordion" id="changesAccordion">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#databaseChanges">
                                            Database Changes
                                        </button>
                                    </h2>
                                    <div id="databaseChanges" class="accordion-collapse collapse show" data-bs-parent="#changesAccordion">
                                        <div class="accordion-body">
                                            <ul>
                                                <li>Added <code>gender</code> column to <code>members</code> table</li>
                                                <li>ENUM type with values: 'Male', 'Female', 'Other', 'Prefer not to say'</li>
                                                <li>Added index for better query performance</li>
                                                <li>Created migration log table for tracking changes</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#formChanges">
                                            Form Changes
                                        </button>
                                    </h2>
                                    <div id="formChanges" class="accordion-collapse collapse" data-bs-parent="#changesAccordion">
                                        <div class="accordion-body">
                                            <ul>
                                                <li>Reordered registration form fields as requested</li>
                                                <li>Added gender dropdown with 4 options</li>
                                                <li>Updated admin add member form</li>
                                                <li>Corrected "Physic Address" to "Physical Address"</li>
                                                <li>Maintained mobile responsive design</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#codeChanges">
                                            Code Changes
                                        </button>
                                    </h2>
                                    <div id="codeChanges" class="accordion-collapse collapse" data-bs-parent="#changesAccordion">
                                        <div class="accordion-body">
                                            <ul>
                                                <li>Updated <code>process_registration.php</code> to handle gender</li>
                                                <li>Modified <code>UserAuthManager</code> class for gender support</li>
                                                <li>Updated validation logic for gender field</li>
                                                <li>Added language translations for gender terms</li>
                                                <li>Updated database queries to include gender</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-success">
                            <h6><i class="bi bi-check-circle me-2"></i>All Changes Completed</h6>
                            <p class="mb-0">
                                The gender field has been successfully added to the member registration system. 
                                All forms have been reordered as requested, and the system maintains full 
                                functionality while being mobile responsive.
                            </p>
                        </div>

                        <div class="text-center mt-4">
                            <a href="admin/dashboard.php" class="btn btn-primary me-2">
                                <i class="bi bi-speedometer2 me-2"></i>Go to Admin Dashboard
                            </a>
                            <a href="register.php" class="btn btn-success">
                                <i class="bi bi-person-plus me-2"></i>Test Registration
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
