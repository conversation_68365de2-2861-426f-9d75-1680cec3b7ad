<?php
/**
 * Add Request Comment AJAX Endpoint
 * 
 * Adds a comment to a request
 */

session_start();
require_once '../../config.php';
require_once '../../includes/automatic_notifications.php';
require_once '../../admin/includes/admin_notification_functions.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'error' => 'Not authenticated']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

$requestId = $input['request_id'] ?? null;
$comment = trim($input['comment'] ?? '');
$responseType = $input['response_type'] ?? 'support';

if (!$requestId || empty($comment)) {
    echo json_encode(['success' => false, 'error' => 'Request ID and comment are required']);
    exit;
}

try {
    // First check if user can comment on this request
    $stmt = $pdo->prepare("
        SELECT pr.privacy_level, pr.member_id, pr.title, pr.allow_comments 
        FROM prayer_requests pr 
        WHERE pr.id = ?
    ");
    $stmt->execute([$requestId]);
    $request = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$request) {
        echo json_encode(['success' => false, 'error' => 'Request not found']);
        exit;
    }
    
    if (!$request['allow_comments']) {
        echo json_encode(['success' => false, 'error' => 'Comments are not allowed on this request']);
        exit;
    }
    
    // Check if user can comment based on privacy level
    $canComment = false;
    if ($request['member_id'] == $_SESSION['user_id']) {
        $canComment = true; // Owner can always comment
    } elseif ($request['privacy_level'] === 'public') {
        $canComment = true; // Public requests allow comments from all
    } elseif ($request['privacy_level'] === 'members') {
        $canComment = true; // Members can comment on member requests
    }
    
    if (!$canComment) {
        echo json_encode(['success' => false, 'error' => 'You cannot comment on this request']);
        exit;
    }
    
    // Check if user has already commented on this request
    $stmt = $pdo->prepare("
        SELECT id FROM prayer_responses
        WHERE prayer_request_id = ? AND member_id = ?
    ");
    $stmt->execute([$requestId, $_SESSION['user_id']]);
    $existingComment = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($existingComment) {
        // Update existing comment instead of inserting new one
        $stmt = $pdo->prepare("
            UPDATE prayer_responses
            SET response_type = ?, comment = ?, created_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");

        $success = $stmt->execute([
            $responseType,
            $comment,
            $existingComment['id']
        ]);

        $commentId = $existingComment['id'];
    } else {
        // Add new comment
        $stmt = $pdo->prepare("
            INSERT INTO prayer_responses (prayer_request_id, member_id, response_type, comment)
            VALUES (?, ?, ?, ?)
        ");

        $success = $stmt->execute([
            $requestId,
            $_SESSION['user_id'],
            $responseType,
            $comment
        ]);

        $commentId = $pdo->lastInsertId();
    }
    
    if ($success) {
        // Send notification to request owner
        if ($request['member_id'] != $_SESSION['user_id']) {
            notifyNewRequestComment(
                $pdo,
                $_SESSION['user_id'],
                $requestId,
                $request['member_id'],
                $request['title'],
                $comment
            );
        }

        // Notify all admins about new comment
        try {
            // Get commenter name
            $stmt = $pdo->prepare("SELECT full_name FROM members WHERE id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $commenterName = $stmt->fetchColumn() ?: 'A member';

            // Get all admin IDs
            $stmt = $pdo->prepare("SELECT id FROM admins WHERE status = 'active'");
            $stmt->execute();
            $adminIds = $stmt->fetchAll(PDO::FETCH_COLUMN);

            $notificationTitle = "New Comment on Request";
            $notificationMessage = "$commenterName commented on request: \"" . $request['title'] . "\"";
            $actionUrl = "../admin/requests.php?id=" . $requestId;

            foreach ($adminIds as $adminId) {
                createAdminNotification(
                    $pdo,
                    $adminId,
                    $notificationTitle,
                    $notificationMessage,
                    'member_comment',
                    $_SESSION['user_id'],
                    'member',
                    $actionUrl,
                    'normal'
                );
            }
        } catch (Exception $e) {
            error_log("Error creating admin notification for new comment: " . $e->getMessage());
        }
        
        // Get the comment with user info
        $stmt = $pdo->prepare("
            SELECT
                prr.*,
                COALESCE(m.full_name, CONCAT(m.first_name, ' ', m.last_name), 'Member') as commenter_name
            FROM prayer_responses prr
            LEFT JOIN members m ON prr.member_id = m.id
            WHERE prr.id = ?
        ");
        $stmt->execute([$commentId]);
        $newComment = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($newComment) {
            $newComment['formatted_time'] = 'Just now';
            $newComment['can_delete'] = true;
            $newComment['commenter_type'] = 'member';
        }
        
        $message = $existingComment ? 'Comment updated successfully' : 'Comment added successfully';

        echo json_encode([
            'success' => true,
            'message' => $message,
            'comment' => $newComment,
            'updated' => (bool)$existingComment
        ]);
    } else {
        $pdoError = $stmt->errorInfo();
        $errorMsg = isset($pdoError[2]) ? $pdoError[2] : 'Failed to add comment';
        error_log('PDO Error adding comment: ' . $errorMsg);
        echo json_encode(['success' => false, 'error' => $errorMsg]);
    }
    
} catch (Exception $e) {
    error_log("Error adding request comment: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Failed to add comment'
    ]);
}
?>
